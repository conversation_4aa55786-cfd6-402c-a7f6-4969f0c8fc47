<script setup lang="ts">
import { ref } from 'vue';

export type Tabs = ITab[];

export interface ITab{
    name: string;
    title: string;
    selected?: boolean;
    onClick?: (tab: ITab, index: number) => Promise<boolean>;
    [key: string]: any;
}

const { tabs } = defineProps<{
    tabs: Tabs
}>();

const slots = defineSlots<{
    default?: (tab: ITab, i: number) => void,
    tab?: (tab: ITab, i: number) => void,
    between?: (tab: ITab, i: number) => void,
}>();

const emit = defineEmits(['change']);
const current = ref(tabs && tabs.length > 0 ? tabs[0] : undefined);

async function onClick(tab: ITab, index: number)
{
    tabs.forEach(element => {
        element.selected = false;
    });

    tab.selected = true;
    current.value = tab;
    
    if (typeof tab.onClick === 'function')
    {
        const response = await tab.onClick(tab, index);
        if (response === true){
            emit('change', tab, index);
        }
    }
    else
    {
        emit('change', tab, index);
    }
}

function filteredTab(tab: ITab): Record<string, any>
{
    const {name, title, onClick, selected, ...rest } = tab;
    return rest;
}

</script>

<template>

    <section class="tabs">

        <template v-for="(tab, i) of tabs" :key="current">

            <button type="button"
                    class="tab-item"
                    :class="{'selected': tab.selected === true}"
                    @click="onClick(tab, i)"
                    v-bind="filteredTab(tab)">
                <slot v-if="slots.tab" name="default" v-bind="tab"></slot>
                <template v-else>{{ tab.title }}</template>
            </button>

            <slot v-if="i < tabs.length - 1" name="between" v-bind="tab"></slot>

        </template>

    </section>

</template>

<style lang="postcss" scoped>

.tabs{
    @apply flex flex-row items-center space-x-3;

}

</style>