<script setup>
import SummaryContainer from "@/components/dashboard/summaryContainer.vue";
import { useIO } from "@proptexx-com/io";
import { computed, watchEffect } from "vue";
import { useRouter } from "vue-router";

const io = useIO();
const router = useRouter();

const hasWorkspace = computed(() => {
  return typeof io.session?.workspaceId === 'string' && io.session.workspaceId.length > 0;
});

watchEffect(() => {
  if (!hasWorkspace.value) {
    router.replace("/workspace");
  }
});
</script>

<template>
  <div v-if="hasWorkspace">
    <SummaryContainer :workspace-id="io.session.workspaceId" />
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
  height: auto;
  padding: 20px;
  background: #f1f3f5;
  border-radius: 8px;
}
.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  color: red;
}
</style>
