import { useSidebar } from "@proptexx-com/vue";
import { h } from "vue";
import { ITreeNode } from "./tree-view";

const sidebar = useSidebar();

export function navigate(name: string, node: ITreeNode<any>)
{
    switch (name)
    {
        case 'addNode': _onAddNode(name, node); break;
        case 'editNode': _onEditNode(name, node); break;
        case 'removeNode': _onRemoveNode(name, node); break;
        default: throw new Error('Unknown click name from NodeEditor');
    }

    return Promise.resolve();
}

function _onAddNode(name: string, node: ITreeNode<any>){

    const component = h('div', 'Add node');
    sidebar.open(node.name + ' : Add Node', component, node);
}

function _onEditNode(name: string, node: ITreeNode<any>){}
function _onRemoveNode(name: string, node: ITreeNode<any>){}