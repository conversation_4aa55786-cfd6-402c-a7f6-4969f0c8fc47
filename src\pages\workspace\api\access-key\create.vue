<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useIO } from "@proptexx-com/io";
import { useSidebar, useToast } from '@proptexx-com/vue';
import { Form, Val } from '@proptexx-com/forms';
import { checkboxList } from '@proptexx-com/forms-vue';

const io = useIO();
const sb = useSidebar();
const toast = useToast();
const secretPreview = ref();
const services = ref();
const noServiceText = 'Before creating access keys, you need to purchase a service plan';

const form = Form.object({
    name: Form.string({
        validators: [Val.isRequired],
        placeholder: 'Ie. production, staging, development..'
    }),
    services: checkboxList(_loadServices, noServiceText, {
        title: 'Services',
        validators: [Val.minLength(1)],
    }),
});

onMounted(async () => {
    services.value = await _loadServices();
});

async function _loadServices()
{
    const response = await io.queryAsync<any[]>({
        identifier: 'workspace.GetServices',
        payload: { serviceType: 'api' }
    }, true);
    
    const result: Record<string, any> = {};
    for (let item of response){
        result[item.id] = item.title;
    }

    return result;
}

async function copyToClipboard(){
    if (!navigator?.clipboard) return;
    await navigator.clipboard.writeText(secretPreview.value);
    toast.success('Copied', 'Copied to clipboard');
}

async function submit()
{
    if (form.invalid) return;

    const response = await io.executeAsync({
        identifier: 'workspace.createClient',
        payload: form.value
    });

    if (!response?.isSuccess || !response?.result?.secret)
    {
        toast.error('Error', response?.errorMessage || 'Could not create the access key');
        return;
    }

    secretPreview.value = response.result.secret;
}
</script>

<template>

    <section>

        <div v-if="secretPreview">
            <p class="text-sm pb-3">Ensure you store this secret key in a secure and reachable location. Due to security measures, you will not have the opportunity to view it again. Should you misplace this key, you will be required to create a new one</p>

            <div class="form-control">
                <input type="text" :value="secretPreview" readonly />
            </div>

            <div class="space-x-2">

                <button type="button" class="btn is-primary" @click="copyToClipboard">
                    <i class="fa fa-clipboard-check"></i>
                    Copy to clipboard
                </button>

                <button type="button" class="btn" @click="() => sb.close(secretPreview !== undefined)">
                    <i class="fa fa-close"></i>
                    Close
                </button>

            </div>

        </div>

        <template v-else-if="!services || Object.keys(services).length <= 0">
            <p>You need to aquire at least one API service</p>
        </template>

        <template v-else>

            <n-form :form="form" @submit.prevent="submit">
            
                <div class="space-x-2">

                    <button type="submit" class="btn is-primary">
                        <i class="fa fa-check"></i>
                        Create
                    </button>

                    <button type="button" class="btn" @click="() => sb.close()">
                        <i class="fa fa-close"></i>
                        Close
                    </button>

                </div>
            
            </n-form>

        </template>

    </section>

</template>