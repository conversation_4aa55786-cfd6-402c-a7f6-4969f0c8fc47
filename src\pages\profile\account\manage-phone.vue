<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { phoneValidator } from '@proptexx-com/forms-vue';
import { useSidebar } from '@proptexx-com/vue';

const { accountId, item } = defineProps<{
    accountId: string,
    item?: any,
}>();

const sb = useSidebar();
const isNew = typeof item === 'undefined';

const form = Form.object({
    accountId: Form.string({ title: null, format: 'input:hidden' }),
    phoneNumber: Form.string({
        validators: [Val.isRequired, phoneValidator],
        readonly: !isNew,
        description: item && !item.verifiedAt ? 'Phone number is not verified' : undefined
    })
});

form.setValue({ accountId, phoneNumber: item?.number })

async function onSubmit()
{
    // const response = await io.executeAsync({
    //     identifier: 'account.addPhone',
    //     payload: form.value
    // });

    // console.log(response);
}
</script>

<template>

    <section>
        <n-form :form="form">
            <div class="space-x-2">
                <button v-if="isNew" type="submit" class="btn is-primary">
                    Add Phone
                </button>
                <button v-if="!isNew && item && !item.verifiedAt" type="button" class="btn is-primary" @click="() => sb.close()">
                    Send verification SMS
                </button>
                <button type="button" class="btn" @click="() => sb.close()">
                    Close
                </button>
            </div>
        </n-form>
    </section>

</template>