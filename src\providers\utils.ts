import { IOClient, useIO } from "@proptexx-com/io";

export function debounce(func: (...args: any[]) => void, delay: number) {
    let debounceTimer: number | undefined;
    return function(...args: any[]) {
        clearTimeout(debounceTimer);
        debounceTimer = window.setTimeout(() => {
            func(...args);
        }, delay);
    }
}

export function delay(ms: number){
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function hasWorkspace(io?: IOClient)
{
    io ||= useIO();
    return typeof io.session?.workspaceId === 'string';
}

export function hasServiceTypes(io?: IOClient)
{
    io ||= useIO();
    const st = io.session?.serviceTypes as string;
    return (typeof st === 'string' && st.trim().length > 0);
}

export function hasServiceType(serviceType: string, io?: IOClient): boolean{
    if (typeof serviceType !== 'string' || serviceType.length <= 0) return false;
    io ||= useIO();
    return io.session?.serviceTypes?.includes(serviceType) === true;
}

export function isRoot(io: IOClient)
{
    return io.hasClaim('$isRoot');
}

