<script setup lang="ts">
import { ref } from 'vue';

const updates = ref(0);

</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>System</h3>
                <div>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">
                
            </div>

        </div>

    </section>

</template>