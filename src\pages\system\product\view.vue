<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import DateTime from '@/components/datetime.vue';
import Money from '@/components/money.vue';

const route = useRoute();
const updates = ref(0);
const productId = computed(() => route.params['id'] as string);

function getQuery()
{
    return {
        identifier: 'system.getProduct',
        payload: { id: productId.value }
    };
}

function update(){
    updates.value++;
}
</script>

<template>

    <section :key="productId">

        <router-link :to="{name: 'products'}" class="link text-xs !flex !pb-2">
            <i class="fa fa-chevron-left"></i>
            <span>Back to products</span>
        </router-link>

        <n-query :config="getQuery" :key="updates" #="{product, orders}">

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Product</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body min-h-32">

                    <dl>
                        <dt>Product Name</dt>
                        <dd>{{ product.title }}</dd>
                        <dt>Payment Type</dt>
                        <dd class="uppercase">{{ product.paymentType }}</dd>
                        <dt>Created At</dt>
                        <dd><DateTime :value="product.createdAt"></DateTime></dd>
                        <dt>Price</dt>
                        <dd><Money :amount="product.priceAmount" :currency="product.currency"></Money></dd>
                        <dt>Description</dt>
                        <dd>{{ product.description || '-'}}</dd>
                        <dt>Summary</dt>
                        <dd>{{ product.summary || '-'}}</dd>
                    </dl>

                    <template v-if="product.content">
                        <hr class="my-4">
                        <p>{{ product.content }}</p>
                    </template>

                </div>
            
            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Orders</h3>
                    </div>
                    <div>
                        <button type="button">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="orders && orders.length > 0" class="overflow-x-auto">


                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Purchased by</th>
                                    <th>Paid at</th>
                                    <th class="w-[85px]">Price</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of orders">
                                    <td>
                                        <router-link :to="{name: 'order', params: { id: item.id }}">
                                            Order #{{ item.orderNbr }}
                                        </router-link>
                                    </td>
                                    <td>
                                        {{ item.workspaceName }}<br>
                                        <small>{{ item.referencePerson }}</small>
                                    </td>
                                    <td>
                                        <span v-if="item.paidAt" class="p-1 rounded bg-green-200">
                                            <DateTime :value="item.paidAt"></DateTime>
                                        </span>
                                        <span v-else class="p-1 rounded bg-red-200">
                                            Pending
                                        </span>
                                    </td>
                                    <td>
                                        <Money :amount="item.totalPrice" :currency="item.currency"></Money>
                                    </td>
                                </tr>
                            </tbody>

                        </table>

                    </div>

                    <div v-else>
                        Product has no sales yet
                    </div>

                </div>

            </div>

        </n-query>

    </section>

</template>