<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from "vue";
import Multiselect from "@vueform/multiselect";

const props = defineProps({
  clients: Array,
  filterProperty: {
    type: String,
    required: true,  // No default, must be provided by the parent component
  },
});

const emit = defineEmits(["update:selectedClients"]);

const selectedClients = ref([]);
const searchQuery = ref("");

// Extract unique labels based on filterProperty
const uniqueClients = computed(() => {
  const values = props.clients.map(client => client[props.filterProperty]).filter(Boolean);
  return [...new Set(values)].map(value => ({ value, label: value }));
});

// Filter options based on search query
const filteredClients = computed(() => {
  if (!searchQuery.value) return uniqueClients.value;
  return uniqueClients.value.filter(client =>
    client.label.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// Emit changes
const onSelectionChange = () => {
  emit("update:selectedClients", selectedClients.value);
};

// Select/Deselect All
const toggleSelectAll = () => {
  if (selectedClients.value.length === filteredClients.value.length) {
    selectedClients.value = [];
  } else {
    selectedClients.value = filteredClients.value.map(client => client.value);
  }
  emit("update:selectedClients", selectedClients.value);
};

onMounted(() => {
  emit("update:selectedClients", selectedClients.value);
});
</script>

<template>
  <div class="client-selector">
    <button @click="toggleSelectAll" class="select-toggle-button">
      {{ selectedClients.length === filteredClients.length ? "Deselect All" : "Select All" }}
    </button>

    <Multiselect
      v-model="selectedClients"
      mode="tags"
      :options="filteredClients"
      :multiple="true"
      :close-on-select="false"
      :searchable="true"
      placeholder="Select labels..."
      @search-change="searchQuery = $event"
      @update:modelValue="onSelectionChange"
    />
  </div>
</template>

<style scoped>
.client-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.select-toggle-button {
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.select-toggle-button:hover {
  background-color: #0056b3;
}
</style>

<style src="@vueform/multiselect/themes/default.css"></style>
