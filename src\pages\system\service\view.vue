<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import ApiTester from '@/components/api-tester.vue';

const route = useRoute();
const updates = ref(0);
const serviceId = computed(() => route.params['id'] as string);

const serviceUrl = computed(() => {
    return `https://api-us.proptexx.com/v2/cv_models/${serviceId.value}/url`;
});

function getQuery()
{
    return {
        identifier: 'system.getService',
        payload: { id: serviceId.value }
    };
}

function update(){
    updates.value++;
}

</script>

<template>

    <section :key="serviceId">

        <router-link :to="{name: 'system-services'}" class="link text-xs !flex !pb-2">
            <i class="fa fa-chevron-left"></i>
            <span>Back to services</span>
        </router-link>

        <n-query :config="getQuery" :key="updates" #="{service, usages}">

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Service</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <dl>
                        <dt>Name</dt>
                        <dd>{{ service.title }}</dd>
                        <dt>Type of service</dt>
                        <dd>{{ service.serviceType }}</dd>
                        <dt>Description</dt>
                        <dd>{{ service.description || '-' }}</dd>
                        <dt>Credit costs</dt>
                        <dd>{{ service.creditCost }} credit per request</dd>
                        <dt>URL</dt>
                        <dd>
                            <code>{{ serviceUrl }}</code>
                        </dd>
                    </dl>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Usage Statistics</h3>
                    </div>
                    <div>
                        <button type="button">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <p>Coming soon</p>
                </div>

            </div>
            <ApiTester v-if="service.serviceType === 'api'" :url="serviceUrl"></ApiTester>

        </n-query>

    </section>

</template>