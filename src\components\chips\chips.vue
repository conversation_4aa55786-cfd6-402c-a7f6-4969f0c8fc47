<script setup lang="ts">
import { ref } from 'vue';
import { Chip } from '@/components/chips/types';

defineOptions({
    inheritAttrs: true
})

const props = defineProps<{
    chips: Chip[] | null | undefined;
    selectable?: boolean;
    classOnSelect?: string;
    onClick?: (e: MouseEvent, chip: Chip, selectedIndex: number) => void;
}>();

const emits = defineEmits<{
    change: [value: Chip[]]
}>();

const selected = ref<Chip[]>([]);

function isSelected(chip: Chip)
{
    if (!props.selectable) return false;
    return selected.value.findIndex(c => c.id === chip.id) >= 0;
}

function _onClick(e: MouseEvent, chip: Chip)
{
    const index = selected.value.findIndex(c => c.id === chip.id);

    if (index >= 0)
    {
        selected.value.splice(index, 1);
    }
    else
    {
        selected.value.push(chip);
    }

    props.onClick && props.onClick(e, chip, index);
    emits('change', selected.value);
}

</script>

<template>

    <div class="flex flex-col sm:flex-row gap-2" v-if="Array.isArray(chips) && chips.length > 0">

        <template v-for="c of chips" :key="c?.id">

            <button type="button"
                    :class="isSelected(c) && (classOnSelect || 'selected')"
                    @click="(e) => _onClick(e, c)">
                <slot :="c">
                    <p>Default template slot is not defined</p>
                    <p>{{ c }}</p>
                </slot>
            </button>

        </template>
    </div>

    <slot v-else name="empty">
        <i>No items</i>
    </slot>

</template>

<style scoped lang="postcss">

button{
    @apply p-1 px-2 bg-gray-200 rounded;

    &.selected{
        @apply bg-blue-300;
    }
}
</style>