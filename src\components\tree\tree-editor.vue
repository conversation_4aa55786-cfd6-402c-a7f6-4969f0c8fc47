<script setup lang="ts">
import { computed, h, ref } from 'vue';
import NTreeView, { ITreeNode } from './tree-view';
import { useSidebar } from '@proptexx-com/vue'
import NodeEditor, { INodeEditorConfig } from './node-editor.vue';
import { navigate } from './tree-editor-navigation';

interface ITreeNodeEditorProps
{
    config: ITreeNodeEditorConfig
}

export interface ITreeNodeEditorConfig
{
    nodes: ITreeNode<any>[];
}

const sidebar = useSidebar();
const { config } = defineProps<ITreeNodeEditorProps>();
const nodes = config.nodes;
const selected = ref<ITreeNode<any>>();

const isValid = computed(() => {
    return nodes && Array.isArray(nodes);
});

function isSelected(item: ITreeNode<any>)
{
    return item.id && item.id === selected.value?.id;
}

function showMenu(node: ITreeNode<any>)
{
    sidebar.open(node.name, NodeEditor, <INodeEditorConfig>{
        node,
        onClick: navigate
    });
}

</script>

<template>

    <n-tree-view v-if="isValid" :nodes="nodes" #="item">
        <div :class="{'selected': isSelected(item) }" @click="selected = item" @dblclick="showMenu(item)">
            {{ item.name }}
        </div>
    </n-tree-view>

    <div v-else>
        Attribute 'config' of interface ITreeNodeEditorConfig must be provided
    </div>

</template>