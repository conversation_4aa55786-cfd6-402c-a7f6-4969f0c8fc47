<script setup lang="ts">
import { useIO } from '@proptexx-com/io';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { toDateStr } from '@/providers/date';
import CreateWorkspaceComp from '@/components/create-workspace.vue';
import DateTime from '@/components/datetime.vue';
import { useSidebar } from '@proptexx-com/vue';
import AccountViewComp from '@/components/domain/account/view.vue';

const io = useIO();
const sb = useSidebar();
const route = useRoute();
const updates = ref(0);
const accountId = computed(() => route.params['id'] as string);

function getQuery()
{
    return {
        profile: { identifier: 'account.GetProfile', payload: { id: accountId.value } },
        phones: { identifier: 'account.GetPhones', payload: { id: accountId.value } },
        emails: { identifier: 'account.GetEmails', payload: { id: accountId.value } },
        workspaces: { identifier: 'account.GetWorkspaces', payload: { id: accountId.value } },
    };
}

function update(){
    updates.value++;
}

function gender(g?: number)
{
    if (g === 1) return 'Female';
    if (g === 2) return 'Male';
    return '';
}

async function onUpdateClick(profile: any)
{
    // const feedback = await sb.open('Update Profile', ManageProfileComp, { profile: result.value.profile });
    // if (feedback === true){
    //     await io.authAsync({});
    //     update();
    // }
}

async function onEmailClick(item?: any)
{
    const props = { accountId: accountId.value, item };
    // const feedback = await sb.open('Email', ManageEmailComp, props);
    update();
}

async function onPhoneClick(item?: any)
{
    const props = { accountId: accountId.value, item };
    // const feedback = await sb.open('Phone', ManagePhoneComp, props);
    update();
}

async function createWorkspace()
{
    const feedback = await sb.open('New workspace', CreateWorkspaceComp);
    if (feedback) update();
}

</script>

<template>

    <section :key="accountId">

        <router-link :to="{name: 'accounts'}" class="link text-xs !flex !pb-2">
            <i class="fa fa-chevron-left"></i>
            <span>Back to accounts</span>
        </router-link>

        <AccountViewComp></AccountViewComp>

    </section>

</template>