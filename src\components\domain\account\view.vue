<script setup lang="ts">
import { useIO } from '@proptexx-com/io';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { toDateStr } from '@/providers/date';
import CreateWorkspaceComp from '@/components/create-workspace.vue';
import DateTime from '@/components/datetime.vue';
import { useSidebar } from '@proptexx-com/vue';
import { isRoot } from '@/providers/utils';

const io = useIO();
const sb = useSidebar();
const route = useRoute();
const router = useRouter();
const updates = ref(0);
const accountId = computed(() => route.params['id'] as string);

function getQuery()
{
    return {
        profile: { identifier: 'account.GetProfile', payload: { id: accountId.value } },
        phones: { identifier: 'account.GetPhones', payload: { id: accountId.value } },
        emails: { identifier: 'account.GetEmails', payload: { id: accountId.value } },
        workspaces: { identifier: 'account.GetWorkspaces', payload: { id: accountId.value } },
    };
}

function update(){
    updates.value++;
}

function gender(g?: number)
{
    if (g === 1) return 'Female';
    if (g === 2) return 'Male';
    return '';
}

async function onUpdateClick(profile: any)
{
    // const feedback = await sb.open('Update Profile', ManageProfileComp, { profile: result.value.profile });
    // if (feedback === true){
    //     await io.authAsync({});
    //     update();
    // }
}

async function onEmailClick(item?: any)
{
    const props = { accountId: accountId.value, item };
    // const feedback = await sb.open('Email', ManageEmailComp, props);
    update();
}

async function onPhoneClick(item?: any)
{
    const props = { accountId: accountId.value, item };
    // const feedback = await sb.open('Phone', ManagePhoneComp, props);
    update();
}

async function createWorkspace()
{
    const feedback = await sb.open('New workspace', CreateWorkspaceComp);
    if (feedback) update();
}

async function changeAccount(accountId: string){
    await io.authAsync({
        'change-account': { accountId }
    });

    router.push({name: 'profile'});
}
</script>

<template>

    <section :key="accountId">

        <n-query :config="getQuery" :key="updates" #="{ profile, phones, emails, workspaces }">

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Account</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <dl>
                        <template v-if="io.session?.isRoot === 'true'">
                            <dt>Id</dt>
                            <dd>{{ profile.id }}</dd>
                        </template>
    
                        <dt>First Name</dt>
                        <dd>{{ profile.firstName }}</dd>
                        <dt>Family Name</dt>
                        <dd>{{ profile.familyName }}</dd>
                        <dt>Member Since</dt>
                        <dd><DateTime :value="profile.createdAt"></DateTime></dd>
    
                        <template v-if="profile.dateOfBirth">
                            <dt>Date of birth</dt>
                            <dd>{{ toDateStr(profile.dateOfBirth) }}</dd>
                        </template>
    
                        <template v-if="profile.gender">
                            <dt>Gender</dt>
                            <dd>{{ gender(profile.gender) }}</dd>
                        </template>
                        <template v-if="emails && emails.length > 0">
                            <dt>Emails</dt>
                            <dd class="py-1 space-x-2">
                                <span :class="item.verifiedAt ? 'bg-green-200': 'bg-red-200'"
                                        class="p-1 rounded cursor-pointer" 
                                        v-for="item of emails" 
                                        :key="item.id" 
                                        @click="() => onEmailClick(item)">
                                    {{ item.email }}
                                </span>
                            </dd>
                        </template>

                        <template v-if="phones && phones.length > 0">
                            <dt>Phones</dt>
                            <dd class="py-1 space-x-2">
                                <span :class="item.verifiedAt ? 'bg-green-200': 'bg-red-200'"
                                        class="p-1 rounded cursor-pointer"
                                        v-for="item of phones"
                                        :key="item.id"
                                        @click="() => onPhoneClick(item)">
                                    {{ item.number }}
                                </span>
                            </dd>
                        </template>

                    </dl>

                </div>

                <div class="card-footer">
                    <button v-if="isRoot(io)" type="button" class="link" @click="() => changeAccount(accountId)">
                        <i class="fa fa-eye"></i>
                        Switch to
                    </button>
                </div>

            </div>

            <div class="card">

                <div class="card-header">

                    <div>
                        <h3>Workspaces</h3>
                    </div>

                    <div>
                        <button type="button" @click="() => update()">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="workspaces && workspaces.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Roles</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="ws of workspaces" :key="ws.id">
                                    <td>
                                        <router-link :to="{name: 'workspace', params: { id: ws.id }}">
                                            {{ ws.title }}
                                        </router-link>
                                    </td>
                                    <td>{{ ws.roles }}</td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        <p>No workspaces</p>
                    </div>
                </div>

            </div>

        </n-query>

    </section>

</template>