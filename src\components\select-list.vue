<script setup lang="ts">
import { FormEntry } from '@proptexx-com/forms';
import { onMounted, ref } from 'vue';

const props = defineProps<{entry: FormEntry, items: Function | any[]}>();
const isLoading = ref(true);
const items = ref<{key: string, value: string}[]>([]);

onMounted(async () => {
    if (typeof props.items === 'function')
    {
        items.value = await props.items();
    }
    else if (Array.isArray(props.items))
    {
        items.value = props.items;
    }
    isLoading.value = false;
});

function setValue(e: Event)
{
    props.entry.setValue(e, true);
}
</script>

<template>

    <select @change="setValue">
        <option value="">{{ isLoading ? 'Loading..' : '' }}</option>
        <option v-for="item of items" :value="item.key">
            {{ item.value }}
        </option>
    </select>

</template>