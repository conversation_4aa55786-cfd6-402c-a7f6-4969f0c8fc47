<script setup lang="ts">
import { IQuery, useIO } from '@proptexx-com/io';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import DateTime from '@/components/datetime.vue';
import { isRoot } from '@/providers/utils';
import { useSidebar } from '@proptexx-com/vue';
import CopyToClipboard from '@/components/copy-to-clipboard.vue';
import MarkAsPaid from './mark-as-paid.vue';
import Money from '@/components/money.vue';

const io = useIO();
const router = useRouter();
const sb = useSidebar();
const updates = ref(0);
const orderId = computed(() => {
    return router.currentRoute.value.params['id'] as string;
});

const q: IQuery = {
    identifier: 'ecommerce.getOrder',
    payload: { id: orderId.value }
};

async function markAsPaid(){
    const feedback = await sb.open('Mark as paid', MarkAsPaid, { orderId: orderId.value });
    if (feedback) updates.value++;
}

async function createPaymentLink()
{
    const response = await io.executeAsync({
        identifier: 'system.GeneratePaymentLink',
        payload: { orderId: orderId.value }
    });

    console.log(response);
    updates.value++;
}

</script>

<template>

    <section :key="orderId">

        <button type="button" class="link text-xs !flex !pb-2" @click="() => router.back()">
            <i class="fa fa-chevron-left"></i>
            <span>Go back</span>
        </button>        

        <n-query :config="q" :key="updates" #="{order, orderLines}">

            <div class="card">
    
                <div class="card-header">
                    <div>
                        <h3>Order #{{ order.orderNbr }}</h3>
                    </div>
                    <div>
                        <button type="button" @click="updates++">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <dl>
                        <dt>Reference</dt>
                        <dd>{{ order.referencePerson }}</dd>
                        <dt>Price</dt>
                        <dd><Money :amount="order.totalPrice" :currency="order.currency"></Money></dd>
                        <dt>Payment Link</dt>
                        <dd class="py-1">
                            <template v-if="order.paymentLink">
                                <CopyToClipboard :text="order.paymentLink"></CopyToClipboard>
                                <a :href="order.paymentLink" class="link">
                                    <i class="fa fa-link"></i>
                                    Go to Stripe Checkout
                                </a>
                            </template>

                            <button v-else type="button" class="link" @click="createPaymentLink">
                                <i class="fa fa-link"></i>
                                Create payment link
                            </button>
                        </dd>
                        <dt>Paid at</dt>
                        <dd>
                            <span v-if="order.paidAt" class="p-1 rounded bg-green-200">
                                <DateTime :value="order.paidAt"></DateTime>
                            </span>
                            <span v-else class="p-1 rounded bg-red-200">
                                Not paid
                            </span>
                        </dd>
                        <dt>Created</dt>
                        <dd><DateTime :value="order.createdAt"></DateTime></dd>
                    </dl>
                </div>

                <div class="card-footer">

                    <button v-if="isRoot(io)" type="button" class="link" @click="markAsPaid">
                        <i class="fa fa-check"></i>
                        Mark as Paid
                    </button>
                </div>
    
            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Order Lines</h3>
                    </div>
                    <div>
                        <button type="button" @click="updates++">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="orderLines && orderLines.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">
                            <tbody>
                                <tr v-for="item of orderLines" :key="item.id">
                                    <td>{{ item.title }}</td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Workspace has no orders
                    </div>

                </div>

            </div>

        </n-query>

    </section>

</template>