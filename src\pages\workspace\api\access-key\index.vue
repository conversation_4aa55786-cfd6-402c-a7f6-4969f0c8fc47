<script setup lang="ts">
import { useSidebar } from '@proptexx-com/vue';
import { update, updates } from '@/providers/workspace-updater';
import DateTime from '@/components/datetime.vue';
import CreateAccessKey from './create.vue';
import EditAccessKey from './edit.vue';

const sb = useSidebar();

const q = {
    identifier: 'workspace.GetClients',
    payload: {  }
};

async function create()
{
    const isSuccess = await sb.open('New Access Key', CreateAccessKey);
    if (!isSuccess) return;
    update();
}

async function edit(accessKey: any)
{
    const isSuccess = await sb.open('Edit Access Key', EditAccessKey, { accessKey });
    if (!isSuccess) return;
    update();
}

</script>

<template>

    <section class="card">

        <div class="card-header">
            <div>
                <h3>Access Keys</h3>
            </div>
            <div>
                <button type="button" class="link text-sm" @click="create">
                    <i class="fa fa-edit"></i>
                    New Access Key
                </button>
                <span class="vertical-separator"></span>
                <button type="button" @click="update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">

            <n-query :config="q" :key="updates" #="items">
    
                <div v-if="items.length > 0" class="overflow-x-auto">

                    <table class="table is-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th class="w-[175px]">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item of items">
                                <td>
                                    <button type="button" class="link" @click="edit(item)">
                                        {{ item.name }}
                                    </button>
                                </td>
                                <td><DateTime :value="item.createdAt"></DateTime></td>
                            </tr>
                        </tbody>
                    </table>

                </div>
    
                <div v-else>
                    <p>To access our services an API key is needed</p>
                    <p>
                        <button type="button" class="link" @click="create">
                            Click here to create one
                        </button>
                    </p>
                </div>
    
            </n-query>

        </div>

    </section>

</template>