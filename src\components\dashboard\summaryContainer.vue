<script setup>
import { ref } from "vue";
import RequestCount<PERSON>hart from "@/components/dashboard/requestCountChart.vue";
import ModelUsersChart from "@/components/dashboard/modelUsersChart.vue";
import BatchCountChart from "@/components/dashboard/batchCountChart.vue";
import SummaryTab from "@/components/dashboard/summaryTab.vue";
import DefaultSelector from "@/components/dashboard/chartDefaultSelector.vue";
import ApiTimeline from '@/components/dashboard/timelineChart.vue';
import Toprendered from '@/components/dashboard/topRendered.vue';


const props = defineProps({
    workspaceId: {
        type: String,
        default: null,
    },
    selectedItemIds: {
        type: Array,
        default: () => [],
    },
    startDate: {
        type: [String, null],
        default: null,
    },
    endDate: {
        type: [String, null],
        default: null,
    },
    activeTab: {
        type: [String, null],
        default: null,
    },
    title: {
        type: String,
        default: null,
    }
});

// State variables
const title = ref(props.title);
const workspaceId = ref(props.workspaceId);
const workspaceIds = ref(props.workspaceId ? [props.workspaceId] : [...props.selectedItemIds]);
const startDate = ref(new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split("T")[0]);
const endDate = ref(new Date().toISOString().split("T")[0]);
const activeTab = ref("summary");
</script>

<template>

    <div class="dashboard-container">
        <h1 v-if="title">{{ title }}</h1>
        <DefaultSelector v-if="!workspaceId" v-model="workspaceIds" />
        <div class="filters">
            <label for="startDate">Start Date:</label>
            <input type="date" id="startDate" v-model="startDate" />
            <label for="endDate">End Date:</label>
            <input type="date" id="endDate" v-model="endDate" />
        </div>
        <div class="tabs">
            <button @click="activeTab = 'summary'" :class="{ active: activeTab === 'summary' }">Summary</button>
            <button @click="activeTab = 'requests'" :class="{ active: activeTab === 'requests' }">API Usage</button>
            <button @click="activeTab = 'users'" :class="{ active: activeTab === 'users' }">AI Models</button>
            <button @click="activeTab = 'batch'" :class="{ active: activeTab === 'batch' }">Batches</button>
            <button @click="activeTab = 'apiTimeline'" :class="{ active: activeTab === 'apiTimeline' }">API
                Timeline</button>
            <button @click="activeTab = 'topRendered'" :class="{ active: activeTab === 'topRendered' }">Top
                Rendered</button>
        </div>
        <div class="charts">
            <SummaryTab v-if="activeTab === 'summary'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
            <RequestCountChart v-if="activeTab === 'requests'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
            <ModelUsersChart v-if="activeTab === 'users'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
            <BatchCountChart v-if="activeTab === 'batch'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
            <ApiTimeline v-if="activeTab === 'apiTimeline'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
            <Toprendered v-if="activeTab === 'topRendered'" :selectedItems="workspaceIds" :startDate="startDate"
                :endDate="endDate" />
        </div>
    </div>
</template>

<style scoped>
.dashboard-container {
    width: 100%;
    height: auto;
    padding: 20px;
    background: #f1f3f5;
    border-radius: 8px;
    overflow-y: auto;
}

.filters {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tabs button {
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    background-color: #ddd;
    border-radius: 5px;
}

.tabs button.active {
    background-color: #007bff;
    color: white;
}

.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tabs button {
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    background-color: #ddd;
    border-radius: 5px;
}

.tabs button.active {
    background-color: #007bff;
    color: white;
}

.charts {
    width: 100%;
}

/* Access Denied Styling */
.access-denied {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    color: red;
}

.access-denied h1 {
    font-size: 2rem;
    margin-bottom: 10px;
}
</style>
