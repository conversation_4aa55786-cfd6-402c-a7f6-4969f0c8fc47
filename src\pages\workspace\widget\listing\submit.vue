<script setup lang="ts">
import { Form, FormString, Val } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import { v4 } from 'uuid';
import { h } from 'vue';

const io = useIO();
const sb = useSidebar();

const form = Form.object({
    identifier: Form.string({
        value: v4(),
        description: 'A unique identifier',
        validators: [Val.isRequired]
    }),
    images: Form.string({
        title: 'Image urls',
        format: (ctrl: FormString) => h('textarea', {
            onInput: (e) => ctrl.setValue(e)
        }),
        description: 'A comma-separated list of image URLs',
        validators: [Val.isRequired],
        value: 'https://storage.googleapis.com/proptexx-store-widget/property_landing/161534.jpg,https://storage.googleapis.com/proptexx/building_architecture.webp',
        style: 'min-height: 150px'
    })
});

async function submit()
{
    if (form.invalid) return;
    console.log('submitting');
    const images = form.value.images.trim().split(',');
    console.log(images);
    const response = await io.executeAsync({
        identifier: 'batch.submit',
        payload: {
            identifier: form.value.identifier,
            images
        }
    });

    console.log(response);
}

</script>

<template>

    <n-form :form="form" @submit.prevent="submit">

        <div class="space-x-2">

            <button type="submit" class="btn is-primary">
                <i class="fa fa-check"></i>
                Create
            </button>

            <button type="button" class="btn" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

    </n-form>

</template>