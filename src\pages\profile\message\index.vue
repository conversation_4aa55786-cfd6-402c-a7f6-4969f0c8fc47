<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useIO } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import NewMessage from './new.vue';
import Tabs, { ITab } from '@/components/tabs.vue';

const io = useIO();
const router = useRouter();
const sb = useSidebar();
const searchQuery = ref(''); // ⬅️ Variável local para armazenar a pesquisa

const tabs: ITab[] = [
    { name: 'inbox', title: 'Inbox', selected: true },
    { name: 'outbox', title: 'Sent' },
    { name: 'important', title: 'Important' },
    { name: 'drafts', title: 'Drafts' },
    { name: 'templates', title: 'Templates' }
];

async function onChange(tab: ITab) {
    router.push(tab.name);
}

async function openNewMessage() {
    sb.open('New Message', NewMessage);
}
</script>

<template>
    
    <section class="flex flex-col bg-white p-3 rounded">

        <div class="flex flex-row justify-between items-center">

            <div>

            </div>

            <div class="flex flex-row justify-between items-center space-x-3">

                <div class="w-[300px] relative">
                    <input v-model="searchQuery" type="text" placeholder="Search by Name, Email or Keywords" class="w-full p-2 border rounded-md shadow-sm focus:outline-none focus:ring focus:border-blue-300" />
                    <i class="fa fa-search absolute right-3 top-3 text-gray-500"></i>
                </div>

                <button class="bg-red-500 text-white px-4 py-2 rounded-lg shadow-md hover:bg-red-600 transition" @click="openNewMessage">
                    + New Message
                </button>
            </div>

        </div>

        <Tabs :tabs="tabs" @change="onChange" class="hidden sm:flex" />

        <div class="flex-1">
            <router-view :search-query="searchQuery"></router-view>
        </div>
    </section>
</template>
