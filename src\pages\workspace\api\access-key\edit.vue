<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { checkboxList } from '@proptexx-com/forms-vue';
import { useIO } from '@proptexx-com/io';

const io = useIO();

const {accessKey} = defineProps<{
    accessKey: any
}>();

const form = Form.object({
    name: Form.string({
        validators: [Val.isRequired],
        placeholder: 'Ie. production, staging..'
    }),
    services: checkboxList(_loadServices, 'No available services', { validators: [Val.minLength(1)]}),
});

async function _loadServices()
{
    const response = await io.queryAsync<any[]>({
        identifier: 'workspace.GetServices',
        payload: {}
    }, true);

    const result: Record<string, any> = {};
    for (let item of response){
        result[item.id] = item.title;
    }
    return result;
}
</script>

<template>

    <section>

        <n-form :form="form" :value="accessKey"></n-form>

    </section>

</template>