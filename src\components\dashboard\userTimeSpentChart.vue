<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { Chart as ChartJ<PERSON>, BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue";

ChartJS.register(BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);
const io = useIO();

const chartData = ref({
  labels: [],
  datasets: [
    {
      label: "Avg Time Spent (minutes)",
      data: [],
      backgroundColor: "rgba(75, 192, 192, 0.7)",
      borderColor: "rgba(75, 192, 192, 1)",
      borderWidth: 1,
    },
  ],
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: true },
    title: { display: true, text: "Average Time Spent per Active User (Daily)" },
  },
  scales: {
    x: { title: { display: true, text: "Date" }, grid: { display: false } },
    y: { title: { display: true, text: "Minutes" }, beginAtZero: true },
  },
};

const isLoading = ref(false); // Track loading state
let isComponentMounted = true; // Track component mount state

const fetchData = async () => {
  isLoading.value = true;
  try {
    const response = await io.queryAsync({
      identifier: "dashboard.getUserTimeSpentPerDay",
      payload: {}, // Backend should filter for last 7 days
    }, true);

    if (!isComponentMounted) return; // Prevent state updates if unmounted

    if (!Array.isArray(response) || response.length === 0) {
      console.warn("No data received for user time spent.");
      return;
    }

    const labels = response.map(entry => entry.date.split("T")[0]);
    const data = response.map(entry => entry.totalMinutes ?? 0);

    chartData.value = {
      labels,
      datasets: [
        {
          label: "Avg Time Spent (minutes)",
          data,
          backgroundColor: "rgba(75, 192, 192, 0.7)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1,
        },
      ],
    };
  } catch (error) {
    console.error("Error fetching time spent data:", error);
  } finally {
    if (isComponentMounted) isLoading.value = false; // Ensure loading state is updated only if mounted
  }
};

onMounted(() => {
  isComponentMounted = true;
  fetchData();
});

onUnmounted(() => {
  isComponentMounted = false; // Prevent memory leaks by stopping updates
});
</script>

<template>
  <div class="chart-container">
    <Loader v-if="isLoading" /> <!-- Show loader while loading -->
    <Bar v-else-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
    <p v-else class="no-data">No data available</p>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #000000;
}
</style>
