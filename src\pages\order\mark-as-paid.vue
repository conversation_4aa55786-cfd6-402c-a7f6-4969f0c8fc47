<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { selectList } from '@proptexx-com/forms-vue';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';

const { orderId } = defineProps<{
    orderId: string
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();

const form = Form.object({
    channel: Form.string({ value: 'stripe', readonly: true, validators: [Val.isRequired] }),
    reference: Form.string({validators: [Val.isRequired]}),
    amountPaid: Form.object({
        price: Form.number({
            validators: [Val.isRequired]
        }),
        currency: selectList({
            'USD': 'US Dollars',
            'EUR': 'Euro',
        }, 'Select Currency', { validators: [Val.isRequired]})
    })
});

async function submit() {
    if (form.invalid) return;

    const response = await io.executeAsync({
        identifier: 'system.MarkOrderAsPaid',
        payload: { ...form.value, orderId }
    });

    if (!response?.isSuccess) {
        toast.error('Error', response?.errorMessage || 'Unable to process');
        return;
    }

    toast.success('Success', 'Order is updated');
    sb.close(true);
}

</script>

<template>

    <section>

        <div class="sidebar-header">

            <button type="submit" form="sidebarForm" class="link">
                <i class="fa fa-save"></i>
                Save
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

        <n-form :form="form" id="sidebarForm" @submit.prevent="submit">
        </n-form>

    </section>

</template>