<script setup lang="ts">
import { useModal } from '@proptexx-com/vue';

const {message} = defineProps<{
    message: string
}>();

const modal = useModal();
</script>

<template>

    <div class="flex flex-col justify-between">

        <div class="pb-8">{{ message }}</div>

        <div class="flex flex-row justify-center">

            <button type="button" class="btn is-primary !px-8" @click="() => modal.close()">
                Close
            </button>

        </div>

    </div>

</template>