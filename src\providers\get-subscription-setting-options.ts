import { ProptexxClient } from "@proptexx/widget-sdk";

export interface SubscriptionSettingOptions {
    environments: { value: string; label: string }[];
    subscriptionTypes: { value: string; label: string }[];
}

export async function getSubscriptionSettingOptions(pt: ProptexxClient, data: { workspaceId: string }): Promise<SubscriptionSettingOptions> {
    try {
        const response = await pt.client.executeAsync({
            identifier: 'workspace.getSubscriptionSettingOptions',
            payload: {
                workspaceId: data.workspaceId
            }
        });

        const options =
            (response && (response as any).data) ||
            (response && (response as any).result) ||
            response;

        if (!options || !options.environments || !options.subscriptionTypes) {
            // For debugging: log the actual response structure
            // eslint-disable-next-line no-console
            console.error('[getSubscriptionSettingOptions] Unexpected response:', response);
            throw new Error('Invalid response from getSubscriptionSettingOptions');
        }
        return options as SubscriptionSettingOptions;
    } catch (error) {
        console.error('[getSubscriptionSettingOptions] Error fetching subscription settings:', error);
        throw new Error('Failed to fetch subscription setting options');
    }
}