<script setup>
import { ref, computed } from "vue";
import { useIO } from '@proptexx-com/io';
import RequestCount<PERSON>hart from "@/components/dashboard/requestCountChart.vue";
import ModelUsersChart from "@/components/dashboard/modelUsersChart.vue";
import BatchCountChart from "@/components/dashboard/batchCountChart.vue";
import SummaryTab from "@/components/dashboard/summaryTab.vue";
import DefaultSelector from "@/components/dashboard/chartDefaultSelector.vue";
import ApiTimeline from '@/components/dashboard/timelineChart.vue';

const io = useIO();
// State variables
const selectedItems = ref([]);
const startDate = ref(new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split("T")[0]);
const endDate = ref(new Date().toISOString().split("T")[0]);
const activeTab = ref("summary");

// Access control: Only allow users where isRoot is true
const isRoot = computed(() => {
  return typeof io.session?.$isRoot === 'string' && io.session.$isRoot === 'true';
});
</script>

<template>
  <div v-if="isRoot" class="dashboard-container">
    <h1>Dashboard Overview</h1>

    <DefaultSelector v-model="selectedItems" />


    <div class="filters">
      <label for="startDate">Start Date:</label>
      <input type="date" id="startDate" v-model="startDate" />

      <label for="endDate">End Date:</label>
      <input type="date" id="endDate" v-model="endDate" />
    </div>

    <div class="tabs">
      <button @click="activeTab = 'summary'" :class="{ active: activeTab === 'summary' }">Summary</button>
      <button @click="activeTab = 'requests'" :class="{ active: activeTab === 'requests' }">API Usage</button>
      <button @click="activeTab = 'users'" :class="{ active: activeTab === 'users' }">AI Models</button>
      <button @click="activeTab = 'batch'" :class="{ active: activeTab === 'batch' }">Batches</button>
      <button @click="activeTab = 'apiTimeline'" :class="{ active: activeTab === 'apiTimeline' }">API Timeline</button>
    </div>

    <div class="charts">
      <SummaryTab v-if="activeTab === 'summary'" :selectedItems="selectedItems" :startDate="startDate" :endDate="endDate" />
      <RequestCountChart v-if="activeTab === 'requests'" :selectedItems="selectedItems" :startDate="startDate" :endDate="endDate" />
      <ModelUsersChart v-if="activeTab === 'users'" :selectedItems="selectedItems" :startDate="startDate" :endDate="endDate" />
      <BatchCountChart v-if="activeTab === 'batch'" :selectedItems="selectedItems" :startDate="startDate" :endDate="endDate" />
      <ApiTimeline v-if="activeTab === 'apiTimeline'" :selectedItems="selectedItems" :startDate="startDate" :endDate="endDate" />
    </div>
  </div>

  <!-- Access Denied Message -->
  <div v-else class="access-denied">
    <h1>Access Denied</h1>
    <p>You do not have permission to view this page.</p>
  </div>
</template>

<style scoped>
 .dashboard-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tabs button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  background-color: #ddd;
  border-radius: 5px;
}

.tabs button.active {
  background-color: #007bff;
  color: white;
}

.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tabs button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  background-color: #ddd;
  border-radius: 5px;
}

.tabs button.active {
  background-color: #007bff;
  color: white;
}

.charts {
  display: flex;
  display: flex;
  width: 100%;
  justify-content: center;
}

/* Access Denied Styling */
.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  color: red;
}

.access-denied h1 {
  font-size: 2rem;
  margin-bottom: 10px;
}
</style>

