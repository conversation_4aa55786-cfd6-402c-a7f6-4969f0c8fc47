<script setup lang="ts">
import { h, ref } from 'vue';
import { Form, FormString, Val } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';
import { FormPaginator, checkboxList, selectList } from '@proptexx-com/forms-vue';
import Loader from '@/components/loader.vue'

const { workspaceId } = defineProps<{
    workspaceId?: string
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();
const isLoading = ref(false);
const updates = ref(0);

const form1 = Form.object({
    title: Form.string({
        title: 'Name of product',
        validators: [Val.isRequired, Val.minLength(4), Val.maxLength(60)]
    }),
    paymentType: selectList({
        single: 'Single Payment',
        recurring: 'Recurring Payment',
        free: 'Free'
    }, '- Make selection -', { validators: [Val.isRequired]}),
    price: Form.object({
        price: Form.number({
            validators: [Val.min(1), Val.pattern(/^[0-9]{1,7}(\.[0-9]{1,2})?$/)],
            min: 1,
            max: 1000000,
            class: 'max-w-[100px]'
        }),
        currency: selectList({
            USD: 'US Dollars',
            EUR: 'Euro'
        }, '- Make selection -', { validators: [Val.isRequired] })
    }, { hidden: true }),
    interval: selectList({
        '1:month': 'Monthly',
        '3:month': 'Quarterly',
        '1:year': 'Annually',
    }, '- Make selection -', { validators: [Val.isRequired], hidden: true }),
    // validFor: Form.object({
    //     amount: Form.number({
    //         value: 1,
    //     }),
    //     unit: selectList({
    //         'day': 'Day',
    //         'month': 'Month',
    //         'year': 'Year',
    //     }, '- Make selection -', { title: null, validators: [Val.isRequired] }),
    // }, { hidden: true }),
});

form1.e('paymentType').onUpdate(entry => {
    const showPrice = ['single', 'recurring'].includes(entry.value);
    const showValidFor = ['single', 'free'].includes(entry.value);
    const showInterval = entry.value === 'recurring';
    form1.e('price').toggle(!showPrice);
    form1.e('interval').toggle(!showInterval);
    // form1.e('validFor').toggle(!showValidFor);
});

const form2 = Form.object({
    description: Form.string({
        title: 'Internal description',
        format: (ctrl: FormString) => h('textarea', {
            onInput: (e) => ctrl.setValue(e)
        }),
        validators: [Val.maxLength(500)],
        style: { 'min-height': '100px'}
    }),
    summary: Form.string({
        format: (ctrl: FormString) => h('textarea', {
            onInput: (e) => ctrl.setValue(e)
        }),
        validators: [Val.maxLength(500)],
        style: { 'min-height': '100px'}
    }),
    content: Form.string({
        format: (ctrl: FormString) => h('textarea', {
            onInput: (e) => ctrl.setValue(e)
        }),
        validators: [Val.maxLength(3000)],
        style: { 'min-height': '100px'}
    })
});

const form3 = Form.object({
    services: checkboxList(_loadServices, 'No available services', { validators: [Val.minLength(1)]}),
});

const pager = new FormPaginator(form1, form2, form3);

async function _loadServices()
{
    const response = await io.queryAsync<any[]>({
        identifier: 'system.GetServices',
        payload: {}
    }, true);

    const result: Record<string, any> = {};
    for (let item of response){
        result[item.id] = item.title;
    }
    return result;
}

async function submit()
{
    if (!pager.current) return;
    if (pager.current.invalid) return;
    if (!pager.next()) return;

    if (pager.completed)
    {
        let isSuccess = false;
        isLoading.value = true;

        try {
            const response = await io.executeAsync({
                identifier: 'system.CreateProduct', 
                payload: { ...pager.data, workspaceId }
            });

            if (!response?.isSuccess === true){
                throw Error(response?.errorMessage);
            }
            
            isSuccess = true;
            toast.success('Success', 'Product has been persisted');
        } catch (error: any)
        {
            toast.error('Ouch!', error?.message || 'An error occured');
            isLoading.value = false;
        }
        finally{
            return sb.close(isSuccess);
        }
    }
}

</script>

<template>

    <section>

        <template v-if="isLoading">
            <Loader></Loader>
        </template>

        <template v-else>

            <n-form v-if="!pager.completed" :key="pager.index.value" :form="pager.current" @submit.prevent="submit">
            
                <div class="py-3 space-x-2">
                    <button type="submit" class="btn is-primary">
                        Proceed
                    </button>
                    <button type="button" class="link" @click="() => sb.close()">
                        Cancel
                    </button>
                </div>
            
            </n-form>

        </template>

    </section>

</template>