<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { phoneValidator, selectList } from '@proptexx-com/forms-vue';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';

const { workspaceId } = defineProps<{
    workspaceId: string
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();

const form = Form.object({
    fullName: Form.string({
        validators: [Val.isRequired, Val.minLength(4)]
    }),
    email: Form.string({
        validators: [Val.isRequired, Val.email]
    }),
    phone: Form.string({
        validators: [phoneValidator]
    }),
    role: selectList({
        'Administrator': 'Administrator',
        'Member': 'Member'
    }, 'Select Role', { validators: [Val.isRequired] })
});

async function submit() {
    if (form.invalid) return;

    const response = await io.executeAsync({
        identifier: 'workspace.addMember',
        payload: { ...form.value, workspaceId }
    });

    if (!response?.isSuccess) {
        toast.error('Error', response?.errorMessage || 'Unable to process');
        return;
    }

    toast.success('Success', 'Member has been added');
    sb.close(response?.result.memberId);
}

</script>

<template>

    <section>

        <div class="sidebar-header">

            <button type="submit" form="sidebarForm" class="link">
                <i class="fa fa-save"></i>
                Save
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

        <n-form :form="form" id="sidebarForm" @submit.prevent="submit">
        </n-form>

    </section>

</template>