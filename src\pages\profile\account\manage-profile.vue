<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { selectList } from '@proptexx-com/forms-vue';
import { useIO } from "@proptexx-com/io";
import { useSidebar, useToast } from '@proptexx-com/vue';

const {profile} = defineProps<{
    profile: any
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();

const form = Form.object({
    id: Form.string({
        value: profile.id,
        readonly: true,
        validators: [Val.isRequired]
    }),
    firstName: Form.string({
        value: profile.firstName,
        validators: [Val.isRequired, Val.minLength(2)]
    }),
    familyName: Form.string({
        value: profile.familyName,
        validators: [Val.isRequired, Val.minLength(2)]
    }),
    gender: selectList({
        1: 'Female',
        2: 'Male'
    }, 'Choose gender',{ value: profile.gender?.toString(), validators: [Val.isRequired]})
});

async function submit()
{
    if (form.invalid)
    {
        return;
    }

    const response = await io.executeAsync({
        identifier: 'account.updateProfile',
        payload: form.value
    });

    if (!response?.isSuccess)
    {
        toast.error('Update failed', response?.errorMessage);
        return;
    }

    toast.success('Profile Updated');
    sb.close(true);
}

</script>

<template>

    <section>

        <n-form :form="form" @submit.prevent="submit">
        
            <div class="space-x-2">

                <button type="submit" class="btn is-primary">
                    <i class="fa fa-check"></i>
                    Update
                </button>

                <button type="button" class="btn" @click="() => sb.close()">
                    <i class="fa fa-close"></i>
                    Cancel
                </button>

            </div>
        
        </n-form>

    </section>

</template>