<script setup>
import { ref, onMounted, onUnmounted, defineEmits, computed } from "vue";
import { useIO } from "@proptexx-com/io";
import Multiselect from "@vueform/multiselect";
import Loader from "../loader.vue"; // Import Loader component

const io = useIO();
const emit = defineEmits(["update:modelValue"]);

const items = ref([]);
const selectedItems = ref([]);
const searchQuery = ref("");
const isLoading = ref(false); // Track loading state
let isComponentMounted = true; // Track component mounted state

// Fetch items from backend
const fetchItems = async () => {
    isLoading.value = true;
    try {
        const response = await io.queryAsync({
            identifier: "dashboard.getChartComboOptions",
            payload: {}
        }, true);

        if (isComponentMounted && Array.isArray(response)) {
            items.value = response.map(item => ({
                value: item.id.toString(),
                name: item.name,
                title: item.title,
                fullName: item.fullName,
                email: item.email,
                apiKey: item.apiKey
            }));
        }
    } catch (err) {
        console.error("Error fetching items:", err);
    } finally {
        if (isComponentMounted) isLoading.value = false;
    }
};

// Function to highlight matching text
const highlightMatch = (text, query) => {
    if (!query || !text) return text;
    const regex = new RegExp(`(${query})`, "gi");
    return text.replace(regex, '<span class="highlight">$1</span>');
};

// Computed property to filter & format options
const filteredItems = computed(() => {
    if (!searchQuery.value) {
        return items.value.map(item => ({
            value: item.value,
            label: item.title // Display title by default
        }));
    }

    const search = searchQuery.value.toLowerCase();
    return items.value
        .map(item => {
            let matchProperty = "";
            let matchValue = "";

            if (item.title?.toLowerCase().includes(search)) {
                matchProperty = "Title";
                matchValue = item.title;
            } else if (item.name?.toLowerCase().includes(search)) {
                matchProperty = "Workspace";
                matchValue = item.name;
            } else if (item.fullName?.toLowerCase().includes(search)) {
                matchProperty = "Owner";
                matchValue = item.fullName;
            } else if (item.email?.toLowerCase().includes(search)) {
                matchProperty = "Email";
                matchValue = item.email;
            }
            else if (item.apiKey?.toLowerCase().includes(search)) {
                matchProperty = "ApiKey";
                matchValue = item.apiKey;
            }

            if (!matchProperty) return null;

            return {
                value: item.value,
                label: `${item.title} <span class='match-info'>(${matchProperty}: ${highlightMatch(matchValue, search)})</span>`
            };
        })
        .filter(Boolean);
});

// Handle selection changes
const onSelectionChange = (value) => {
    selectedItems.value = value;
    emit("update:modelValue", selectedItems.value);
};

// Select/Deselect All
const toggleSelectAll = () => {
    if (selectedItems.value.length === filteredItems.value.length) {
        selectedItems.value = [];
    } else {
        selectedItems.value = filteredItems.value.map(item => item.value);
    }
    emit("update:modelValue", selectedItems.value);
};

onMounted(() => {
    isComponentMounted = true;
    fetchItems();
});

onUnmounted(() => {
    isComponentMounted = false; // Prevent memory leaks
});
</script>

<template>
    <div class="chartDefault-selector">
        <Loader v-if="isLoading" /> <!-- Show loader when loading -->

        <button @click="toggleSelectAll" class="select-toggle-button" :disabled="isLoading">
            {{ selectedItems.length === filteredItems.length ? "Deselect All" : "Select All" }}
        </button>

        <Multiselect v-model="selectedItems" mode="tags" :options="filteredItems" :multiple="true"
            :close-on-select="false" :searchable="true" :filter-results="false" placeholder="Select Workspaces..."
            @search-change="searchQuery = $event" @update:modelValue="onSelectionChange" :disabled="isLoading">
            <template v-slot:option="{ option }">
                <span v-html="option.label"></span>
            </template>
        </Multiselect>
    </div>
</template>

<style scoped>
.chartDefault-selector {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-family: Arial, sans-serif;
    width: 100%;
}

.select-toggle-button {
    padding: 6px 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.select-toggle-button:hover {
    background-color: #0056b3;
}

.select-toggle-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.match-info {
    font-size: 12px;
    color: gray;
}

.highlight {
    background-color: yellow;
    font-weight: bold;
}
</style>

<style src="@vueform/multiselect/themes/default.css"></style>
