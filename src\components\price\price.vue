<script setup lang="ts">
import { PriceResult, type PriceValue } from "./index";
import { ref, defineEmits } from "vue";

const props = defineProps<{
    value: string;
    currencies?: Record<string, string> | (() => Record<string, string>) | (() => Promise<Record<string, string>>);
}>();

const emit = defineEmits(['update:value']);

const splitted = typeof props.value === 'string'
        ? props.value.split(_getSeparator(props.value))
        : [];

const integer = ref(splitted[0]?.trim() || '0');
const fraction = ref(splitted[1]?.trim() || '00');

function _getSeparator(value: string)
{
    if (value.includes(','))
    {
        return ',';
    }
    else if (value.includes('.'))
    {
        return '.';
    }
    return ' ';
}

function onInteger()
{
    const c = parseInt(integer.value);
    if (isNaN(c))
    {
        integer.value = '0';
    }
    
    onUpdate();
}

function onFraction()
{
    const c = parseInt(fraction.value);
    if (isNaN(c))
    {
        fraction.value = '00';
    }
    
    onUpdate();
}

function onUpdate()
{
    emit('update:value', `${integer.value || '0'} ${fraction.value || '00'}`);
}

</script>

<template>

    <section>
        <input type="text" minlength="1" maxlength="7" v-model="integer" @input="onInteger"/>
        <input type="text" minlength="2" maxlength="2" v-model="fraction" @input="onFraction" />
    </section>

</template>