<script setup lang="ts">
import { hasServiceTypes } from '@/providers/utils';
import { useIO } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import { computed, ref } from 'vue';
import SelectPlan from '@/components/select-plan.vue';
import Loader from '@/components/loader.vue'; // Import Loader component

const io = useIO();
const sb = useSidebar();
const updates = ref(0);
const isLoading = ref(false); // Reactive variable for loading state

const serviceTypes = computed(() => {
    const st = io.session?.serviceTypes as string;
    if (!st || st.trim().length <= 0) return [];
    return st.split(' ');
});

async function selectPlan() {
    isLoading.value = true; // Set loading state to true
    try {
        const feedback = await sb.open('Select Service Plan', SelectPlan, {});
        // Handle feedback if necessary
    } catch (error) {
        console.error('Error selecting plan:', error);
    } finally {
        isLoading.value = false; // Reset loading state
    }
}
</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>{{ io.session?.workspaceName }}</h3>
                <div v-if="hasServiceTypes()">
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">

                <!-- Display Loader when isLoading is true -->
                <Loader v-if="isLoading" />

                <template v-else-if="hasServiceTypes()">
                    <h2>Dashboard features are coming soon</h2>
                    <p>We are constantly evolving and very soon our workspace dashboard will be launched</p>
                    <p>In the meantime, navigate the menu to see more on the services you have access to</p>
                </template>

                <template v-else>
                    <h2 class="pb-1">Unlock Our Powerful AI Services</h2>
                    <p>To take advantage of our cutting-edge AI capabilities, including generative outputs for real estate images and advanced computer vision models, you need to subscribe to a service plan.</p>

                    <h5 class="pt-5 pb-1">Don't have access yet?</h5>
                    <p>It looks like you haven't selected a service plan. Choose a plan that fits your needs and get started today.</p>
                    <p class="pt-4 pb-1">
                        <button type="button" class="link" @click="selectPlan">
                            Click here to explore our plans and get started!
                        </button>
                    </p>
                </template>

            </div>

        </div>

    </section>

</template>