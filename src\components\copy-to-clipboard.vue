<script setup lang="ts">
import { useToast } from '@proptexx-com/vue';

const { text } = defineProps<{
    text: string
}>();

const toast = useToast();

async function copyToClipboard(){
    if (!navigator?.clipboard || !text) return;
    
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;

    const toCopy = textarea.value;
    try {
        await navigator.clipboard.writeText(toCopy)
        toast.success('Copied', 'Copied to clipboard');
    } catch (error) {
        toast.error('Failed', 'Could not copy to clipboard');
    }
    finally{
        textarea.remove();
    }
}
</script>

<template>

    <div class="copy-to-clipboard">
        <code v-html="text"></code>
        <button class="link" @click="copyToClipboard">
            <i class="fa fa-copy"></i>
        </button>
    </div>

</template>
