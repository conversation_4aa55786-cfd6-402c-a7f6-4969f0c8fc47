<script setup lang="ts">
import { h, ref } from 'vue';
import { Form, FormEntry, Val } from '@proptexx-com/forms';
import { selectList } from '@proptexx-com/forms-vue';
import { useIO } from "@proptexx-com/io";
import { useSidebar, useToast } from '@proptexx-com/vue';

const sb = useSidebar();
const io = useIO();
const toast = useToast();

const isLoading = ref(true); // Loading state

const form = Form.object({
    receiver: selectList(_fetchContacts,        
        'Select receiver',
        { validators: [Val.isRequired] }),
    subject: Form.string([Val.isRequired, Val.maxLength(50)]),
    content: Form.string({
        format: (e: FormEntry) => h('textarea', {
            onInput: (v) => e.setValue(v, true)
        }),
        style: { minHeight: '200px' }
    }),
});

// Fetch contacts data and format for selectList
async function _fetchContacts(): Promise<Record<string, string>> {
    try {
        const response = await io.queryAsync({
            identifier: 'message.getContacts',
            payload: {}
        }, true);

        if (!Array.isArray(response)) return {};

        // Convert array to object { id: "Name" }
        return response.reduce((acc, x) => {
            acc[x.id] = x.name;
            return acc;
        }, {} as Record<string, string>); 
    } catch (error) {
        toast.error('Fetch Contacts', 'An error occurred while fetching contacts');
        return {};
    } finally {
        isLoading.value = false; // Stop loading
    }
}

// Submit the form data
async function submit() {
    console.log('Submitting payload:', JSON.stringify(form.value, null, 2));
    if (form.value.invalid) return;

    const response = await io.executeAsync({
        identifier: 'message.NewMessage',
        payload: form.value
    });

    if (!response?.isSuccess) {
        toast.error('New Message', response?.errorMessage || 'Could not send message');
        return;
    }

    toast.success('New Message', 'Message was sent');
    sb.close(true);
}
</script>

<template>
    <section>
        <n-form :form="form" @submit.prevent="submit">
            <div class="space-x-3">
                <button type="submit" class="btn is-primary" :disabled="form.value.invalid">
                    <i class="fa fa-paper-plane"></i>
                    Send
                </button>
                <button type="button" class="btn" @click="() => sb.close()">
                    <i class="fa fa-close"></i>
                    Close
                </button>
            </div>
        </n-form>
    </section>
</template>
