<script setup lang="ts">
import { useIO } from "@proptexx-com/io";
import { useSidebar } from '@proptexx-com/vue';

const io = useIO();
const sb = useSidebar();

const providers = [{
    id: 'google',
    title: 'Connect with Google'
},{
    id: 'microsoft',
    title: 'Connect with Microsoft'
}];

</script>

<template>

    <section class="space-y-1">

        <div class="py-2">
            <button type="button" class="btn" @click="() => sb.close()">Close</button>
        </div>

    </section>

</template>