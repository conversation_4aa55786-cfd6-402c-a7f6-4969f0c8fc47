import { useIO } from '@proptexx-com/io';
import { AccountModel, ContactModel } from "./types";
import { ref } from "vue";

export class ContactManager
{
    private readonly _selections: AccountModel[];
    private readonly _searchResult: AccountModel[];

    public searchValue: string | undefined;
    public updates = ref(0);

    constructor(selections?: AccountModel[])
    {
        this._selections = selections || [];
        this._searchResult = [];
    }

    public get selections()
    {
        return this._selections;
    }

    public get searchResult()
    {
        return this._searchResult;
    }

    async searchAsync(searchVal?: string)
    {
        this.searchValue = searchVal;

        if (!searchVal || searchVal.length <= 0)
        {
            this._searchResult.splice(0);
            this.updates.value++;
            return;
        }
    
        const io = useIO();
        const response: AccountModel[] = await io.queryAsync({
            identifier: 'crm.account.searchContact',
            payload: { search: searchVal }
        }, true);
    
        this._searchResult.splice(0);

        for (let item of response)
        {
            this._searchResult.push(item);
        }

        this.updates.value++;
    }

    public toggle(selectedAccount: AccountModel, selectedContact: ContactModel) {

        const items: AccountModel[] = this._selections;

        // Find the index of the account in the array
        const accountIndex = items.findIndex(acc => acc.id === selectedAccount.id);

        if (accountIndex !== -1) {
            // Account exists in the array
            const account = items[accountIndex];
    
            // Find the index of the contact in the account
            const contactIndex = account.contacts.findIndex(cont => cont.id === selectedContact.id);
    
            if (contactIndex !== -1) {
                // Contact is already selected, remove it
                account.contacts.splice(contactIndex, 1);
            } else {
                // Add the selected contact to the account
                account.contacts.push(selectedContact);
            }
    
            // If no contacts are selected in the account, remove the account from the array
            if (account.contacts.length === 0) {
                items.splice(accountIndex, 1);
            } else {
                // Update the account in the array for reactivity
                items.splice(accountIndex, 1, {...account});
            }
        } else {
            // Account does not exist in the array, add it with the selected contact
            const newAccount = {
                ...selectedAccount,
                contacts: [selectedContact]
            };
            items.push(newAccount);
        }

        this.updates.value++;
    }
}
