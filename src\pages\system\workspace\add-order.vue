<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { checkboxList, selectList } from '@proptexx-com/forms-vue';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';
import CopyToClipboard from '@/components/copy-to-clipboard.vue';
import CreateProduct from '../product/create.vue';
import { ref } from 'vue';
import { money } from '@/providers/money';

const { workspaceId, workspaceName } = defineProps<{
    workspaceId: string,
    workspaceName: string,
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();
const updates = ref(0);

const form = Form.object({
    product: selectList(_loadProducts, 'Select product', { validators: [Val.isRequired, Val.min(1), Val.max(1)]}),
    initiatedBy: selectList(_loadWorkspaces, 'Select', { validators: [<PERSON>.isRequired, Val.min(1), <PERSON>.max(1)]}),
    onBehalfOf: Form.string({
        value: workspaceName,
        readonly: true
    }),
    // applyCoupon: checkboxList(_loadCoupons, 'No coupons')
});

async function _loadWorkspaces(): Promise<Record<any, any>>
{
    const response = await io.queryAsync({
        identifier: 'account.GetWorkspaces',
        payload: { }
    }, true);

    if (!Array.isArray(response)) return [];

    return response.map(x => {
        return {
            key: x.name,
            value: `${x.name}`
        };
    });
}

async function _loadProducts(): Promise<Record<any, any>>
{
    const response = await io.queryAsync({
        identifier: 'workspace.GetProductPlans',
        payload: { workspaceId }
    }, true);

    if (!Array.isArray(response)) return [];

    return response.map(x => {
        return {
            key: x.id,
            value: `${x.title} (${money(x.priceAmount, x.currency)})`
        };
    });
}

async function _loadCoupons(): Promise<Record<any, any>>
{
    const response = await io.queryAsync({
        identifier: 'workspace.GetCoupons',
        payload: { workspaceId }
    }, true);

    if (!Array.isArray(response)) return [];

    return response.map(x => {
        return {
            key: x.id,
            value: `${x.title} (${money(x.priceAmount, x.currency)})`
        };
    });
}

async function newProduct(){
    const feedback = await sb.open('Create Product', CreateProduct, { workspaceId });
    if (feedback) updates.value++;
}

async function submit() {
    if (form.invalid) return;

    const payload = { ...form.value, workspaceId };
    console.log(payload);
    const response = await io.executeAsync({
        identifier: 'workspace.CreateOrder',
        payload
    });

    if (!response?.isSuccess) {
        toast.error('Error', response?.errorMessage || 'Unable to process');
        return;
    }

    toast.success('Success', 'Order has been created');
    sb.close(response?.result?.orderId);
}

</script>

<template>

    <section>
        <div class="sidebar-header">

            <button type="submit" form="sidebarForm" class="link">
                <i class="fa fa-save"></i>
                Save
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="newProduct">
                <i class="fa fa-edit"></i>
                New Product
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

        <n-form :form="form" id="sidebarForm" :key="updates" @submit.prevent="submit">
        </n-form>

    </section>

</template>