<script setup lang="ts">
import DateTime from '@/components/datetime.vue';
import { useSidebar } from '@proptexx-com/vue';
import CopyToClipboard from '@/components/copy-to-clipboard.vue';

const {apiKey} = defineProps<{apiKey: string}>();
const sb = useSidebar();

</script>

<template>

    <section>

        <dl>
            <dt>Key</dt>
            <dd>
                <div class="container-overflow">
                    {{ apiKey }}
                    <CopyToClipboard :text="apiKey"></CopyToClipboard>
                </div>
            </dd>
            
            <dt>Expires At</dt>
            <dd>Never</dd>

            <dt>Created At</dt>
            <DateTime :value="new Date()"></DateTime>
        </dl>

        <div class="py-4">
            <button type="button" class="btn is-primary" @click="() => sb.close()">
                Close
            </button>
        </div>

    </section>

</template>