<script setup lang="ts">
import { toDateTimeStr, toTimeAgo } from '@/providers/date';
import { ref } from 'vue';

const { value } = defineProps<{
    value: string | number | Date | undefined
}>();

const showExact = ref(false);

</script>

<template>
    <time v-if="!!value"
        :datetime="value.toString()"
        :title="toDateTimeStr(value)"
        class="select-none cursor-pointer"
        @click="showExact = !showExact">
        {{ showExact ? toDateTimeStr(value) : toTimeAgo(value) }}
    </time>
</template>