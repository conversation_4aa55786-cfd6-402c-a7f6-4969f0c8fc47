<script setup lang="ts">
import AccountList from '@/components/domain/account/list.vue';
import { IQuery } from '@proptexx-com/io';

const q: IQuery = {
    identifier: 'workspace.getMembers',
    payload: {}
};

</script>

<template>
    <AccountList
        title="Members"
        empty-text="No members in this workspace yet"
        view-route-name="member"
        :query="q"></AccountList>
</template>