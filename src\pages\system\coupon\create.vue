<script setup lang="ts">
import { VNode, ref } from 'vue';
import { Form, FormEntry, Val } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';
import Loader from '@/components/loader.vue'

const io = useIO();
const sb = useSidebar();
const toast = useToast();
const isLoading = ref(false);
const updates = ref(0);
const form = Form.object({
    code: Form.string({
        title: 'Discount code',
        minLength: 4,
        maxLength: 15,
        validators: [<PERSON>.isRequired, <PERSON>.min<PERSON>ength(4), <PERSON>.maxLength(15)],
        class: 'uppercase'
    }),
    discount: Form.number({
        title: 'Percentage Discount',
        min: 1,
        max: 100,
        maxLength: 3,
        validators: [Val.isRequired, Val.min(1), Val.max(100)],
    }),
});

async function submit()
{
    if (form.invalid) return;

    let isSuccess = false;
    isLoading.value = true;

    try {
        const response = await io.executeAsync({
            identifier: 'system.CreateCoupon', 
            payload: form.value
        });

        if (!response?.isSuccess === true){
            throw Error(response?.errorMessage);
        }
        
        isSuccess = true;
        toast.success('Success', 'Coupon has been added');
    } catch (error: any)
    {
        toast.error('Ouch!', error?.message || 'An error occured');
        isLoading.value = false;
    }
    finally{
        return sb.close(isSuccess);
    }
}
</script>

<template>

    <section>

        <template v-if="isLoading">
            <Loader></Loader>
        </template>

        <template v-else>

            <n-form :key="form.updates" :form="form" @submit.prevent="submit">
            
                <div class="py-3 space-x-2">
                    <button type="submit" class="btn is-primary">
                        Save
                    </button>
                    <button type="button" class="link" @click="() => sb.close()">
                        Cancel
                    </button>
                </div>
            
            </n-form>

        </template>

    </section>

</template>