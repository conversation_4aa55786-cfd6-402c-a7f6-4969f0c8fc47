<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { update, updates } from '@/providers/workspace-updater';
import DateTime from '@/components/datetime.vue';
import { useSidebar } from '@proptexx-com/vue';
import CreateWorkspace from './create.vue';
import { useRouter } from 'vue-router';
import cardActions from '@/components/card-actions.vue';
import Loader from '@/components/loader.vue'; // Import Loader component

const router = useRouter();
const sb = useSidebar();

const searchQuery = ref('');
const sortKey = ref('');
const sortOrder = ref(1);
const currentPage = ref(1);
const pageSize = ref(50);
const startDate = ref(new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split("T")[0]);
const endDate = ref(new Date().toISOString().split("T")[0]);
const isLoading = ref(false);

const q = computed(() => ({
    identifier: 'system.getWorkspaces',
    payload: {
        startDate: startDate.value,
        endDate: endDate.value,
        pageSize: pageSize.value,
        pageNumber: currentPage.value,
        sortBy: sortKey.value,
        sortOrder: sortOrder.value === 1 ? 'ASC' : 'DESC',
        filter: searchQuery.value,
    },
}));

function paddingStyle(depth: number) {
    if (!depth || depth <= 0) return '';
    const indent = 30 * (depth - 1);
    return `padding-left: ${indent}px`;
}

async function create() {
    const workspaceId = await sb.open('Create Workspace', CreateWorkspace);
    if (workspaceId) {
        router.push({ name: 'workspace', params: { id: workspaceId } });
    }
}

const getChangeClass = (value: number) => {
    if (value > 0) return 'text-green-500';
    if (value < 0) return 'text-red-500';
    return 'text-gray-500';
};

function setSort(key: string) {
    if (sortKey.value === key) {
        sortOrder.value *= -1;
    } else {
        sortKey.value = key;
        sortOrder.value = 1;
    }
    refreshWorkspaces();
}

function goToPage(page: number) {
    currentPage.value = page;
    refreshWorkspaces();
}

function getPageNumbers(total: number) {
    return Array.from({ length: total }, (_, i) => i + 1);
}

function changePageSize(size: number) {
    pageSize.value = size;
    currentPage.value = 1;
    refreshWorkspaces();
}

watch([searchQuery, startDate, endDate], () => {
    currentPage.value = 1;
    refreshWorkspaces();
});

// Wrap API call with loading state
async function refreshWorkspaces() {
    isLoading.value = true;
    try {
        await update();
        sb.close();
    } finally {
        isLoading.value = false;
    }
}
</script>

<template>
    <section>
        <div class="card">
            <!-- Card Header -->
            <div class="card-header">
                <h3>Workspaces</h3>
                <card-actions :items="[
                    { title: 'Create Workspace', icon: 'fa fa-edit', onClick: () => create() },
                    { title: '_Refresh', icon: 'fa fa-refresh', onClick: () => refreshWorkspaces() },
                ]"></card-actions>
            </div>

            <!-- Card Body -->
            <div class="card-body min-h-32">
                <!-- Filter Input -->
                <div class="mb-4">
                    <input v-model="searchQuery" type="text" placeholder="Filter by workspace name"
                        class="txt-filter" />
                </div>

                <div class="filters">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" v-model="startDate" />

                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" v-model="endDate" />
                </div>

                <!-- Loader -->
                <Loader v-if="isLoading" class="my-4" />

                <!-- Workspace Table -->
                <n-query :config="q" :key="updates" #="result">
                    <div class="overflow-x-auto" v-if="!isLoading">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Workspace</th>
                                    <th @click="setSort('total_api_requests')" class="cursor-pointer">
                                        Total API requests
                                        <span v-if="sortKey === 'total_api_requests'">
                                            {{ sortOrder === 1 ? '▲' : '▼' }}
                                        </span>
                                    </th>
                                    <th @click="setSort('yesterday_api_requests')" class="cursor-pointer">
                                        Yesterday API requests
                                        <span v-if="sortKey === 'yesterday_api_requests'">
                                            {{ sortOrder === 1 ? '▲' : '▼' }}
                                        </span>
                                    </th>
                                    <th @click="setSort('today_api_requests')" class="cursor-pointer">
                                        Today API requests
                                        <span v-if="sortKey === 'today_api_requests'">
                                            {{ sortOrder === 1 ? '▲' : '▼' }}
                                        </span>
                                    </th>
                                    <th @click="setSort('trend')" class="cursor-pointer">
                                        Trend
                                        <span v-if="sortKey === 'trend'">
                                            {{ sortOrder === 1 ? '▲' : '▼' }}
                                        </span>
                                    </th>
                                    <th class="!text-center">Services</th>
                                    <th class="!text-center">Members</th>
                                    <th class="w-[140px]">Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in result.data" :key="item.id">
                                    <td>
                                        <router-link :to="{ name: 'workspace', params: { id: item.id } }"
                                            :style="paddingStyle(item.depth)">
                                            {{ item.title }}
                                        </router-link>
                                    </td>
                                    <td>{{ item.totalApiRequests }}</td>
                                    <td>{{ item.yesterdayApiRequests }}</td>
                                    <td>{{ item.todayApiRequests }}</td>
                                    <td>
                                        <div class="flex items-center">
                                            <span v-if="item.trend !== 0">
                                                {{ item.trend }}%
                                            </span>
                                            <span v-if="item.trend" class="trend-icon ml-2"
                                                :class="getChangeClass(item.trend)">
                                                <span v-if="item.trend > 0">▲</span>
                                                <span v-else-if="item.trend < 0">▼</span>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="text-center">{{ item.numServices }}</td>
                                    <td class="text-center">{{ item.numAccounts }}</td>
                                    <td>
                                        <DateTime :value="item.createdAt"></DateTime>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="pagination">
                        <button :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">
                            <i class="fa fa-chevron-left"></i>
                        </button>
                        <template v-for="page in getPageNumbers(result.totalPages)" :key="page">
                            <button
                                v-if="page === 1 || (page >= currentPage - 2 && page <= currentPage + 2)"
                                :class="{ 'font-bold': page === currentPage }" @click="goToPage(page)">
                                {{ page }}
                            </button>
                        </template>
                        <span v-if="currentPage < result.totalPages - 2 && result.totalPages > 5">...</span>
                        <button :disabled="currentPage === result.totalPages" @click="goToPage(currentPage + 1)">
                            <i class="fa fa-chevron-right"></i>
                        </button>
                        <span>{{ currentPage }}/{{ result.totalPages }}</span>
                        <label for="pageSize">Page Size:</label>
                        <select id="pageSize" v-model="pageSize" @change="changePageSize(Number((<HTMLSelectElement>$event.target).value))">
                            <option :value="10">10</option>
                            <option :value="20">20</option>
                            <option :value="50">50</option>
                        </select>
                    </div>
                </n-query>
            </div>
        </div>
    </section>
</template>

<style scoped>
.filters {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.txt-filter {
    box-sizing: border-box;
    width: 100%;
    border-radius: 0.5rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    padding: 0.5rem;
    outline: 2px solid transparent;
    outline-offset: 2px;
}
</style>
