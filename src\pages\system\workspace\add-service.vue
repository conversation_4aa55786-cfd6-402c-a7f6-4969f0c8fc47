<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { checkboxList, selectList } from '@proptexx-com/forms-vue';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';
import { ref, computed, watch, nextTick } from 'vue';
import SubscriptionOptions from '@/components/subscription-option.vue';
import { spec } from 'node:test/reporters';


const { workspaceId } = defineProps<{
    workspaceId: string
}>();

const io = useIO();
const sb = useSidebar();
const toast = useToast();

const form = Form.object({
    services: checkboxList(_loadServices, 'No available services', { validators: [Val.minLength(1)]}),
});

const isLimited = ref(false);
const requestLimit = ref();
const showRequestLimit = computed(() => isLimited.value === true);
const requestLimitInput = ref<HTMLInputElement | null>(null);

// Subscription options state
const subscription = ref({
  subscriptionType: 'Period',
  isDemo: false,
  quota: null,
  specific: '',
  duration: 1,
  durationPeriod: undefined,
  expireMode: 'specific',
});

async function _loadServices() {
    const response = await io.queryAsync<any[]>({
        identifier: 'workspace.GetAvailableServices',
        payload: { workspaceId }
    }, true);
    const result: Record<string, any> = {};
    for (let item of response) {
        if (item.hasService !== false) continue;
        result[item.id] = item.title;
    }
    return result;
}


async function submit(){

    if (form.invalid) return;

    const payload = { 
        ...form.value, 
        workspaceId,
        isDemo: subscription.value.isDemo,
        subscription: {
            type: subscription.value.subscriptionType,
            duration: subscription.value.duration,
            quota: subscription.value.quota,
            specific: subscription.value.specific,
            durationPeriod: subscription.value.durationPeriod,
            expireMode: subscription.value.expireMode
        }
    };
    
    // Add request limit if needed (legacy support)
    if (isLimited.value) {
        payload.requestLimit = requestLimit.value;
    }
    
    const response = await io.executeAsync({
        identifier: 'system.AddServicesToWorkspace',
        payload
    });

    if (!response?.isSuccess){
        toast.error('Error', response?.errorMessage || 'Unable to process');
        return;
    }

    toast.success('Success', 'Services has been added to workspace');
    sb.close(true);
}

watch(showRequestLimit, (val) => {
    if (val) {
        nextTick(() => {
            requestLimitInput.value?.focus();
        });
    }
});

watch(subscription, () => {
  if (subscription.value.durationPeriod) {
    if (typeof subscription.value.durationPeriod !== 'object') {
      console.warn('durationPeriod is not an object:', subscription.value.durationPeriod);
    }
  } else {
    console.log('durationPeriod is undefined');
  }
}, { deep: true });
</script>

<template>

    <section>
        
        <div class="sidebar-header">

            <button type="submit" form="sidebarForm" class="link">
                <i class="fa fa-save"></i>
                Save
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

        <!-- Subscription Options Component -->
        <SubscriptionOptions v-model="subscription" />

        <n-form :form="form" id="sidebarForm" @submit.prevent="submit" style="font-size: 16px !important;">
        </n-form>
        
    </section>

</template>