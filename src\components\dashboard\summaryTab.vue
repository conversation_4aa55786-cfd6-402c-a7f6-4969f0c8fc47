<template>
  <div class="dashboard-container">
    <Loader v-if="isLoading" />
    <!-- Show loader while loading -->
    <div v-if="hasData" class="cards-grid">
      <div
        v-for="({ key, value }, index) in filteredSummaryData"
        :key="index"
        :class="['card', getCategory(key)]"
      >
        <div class="card-header">
          <div class="metric-label">{{ formatLabel(key) }}</div>
          <div
            v-if="getPreviousValue(key) !== undefined"
            class="trend-icon"
            :class="getChangeClass(key)"
          >
            <span v-if="isNumeric(value)">
              <span v-if="getAbsoluteChange(key) > 0">▲</span>
              <span v-else-if="getAbsoluteChange(key) < 0">▼</span>
            </span>
          </div>
        </div>
        <div class="card-body">
          <div class="metric-value">{{ formatValue(value, key) }}</div>
          <div
            v-if="getPreviousValue(key) !== undefined"
            class="metric-change"
          >
            {{ formatChange(key) }}
          </div>
        </div>
      </div>
    </div>
    <div v-else class="loading">
      <p v-if="props.loadingMessage">{{ props.loadingMessage }}</p>
      <p v-else class="no-data">No data available</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useIO } from "@proptexx-com/io";

let isComponentMounted = true; // Track component mount state
const isLoading = ref(false); // Track loading state
const io = useIO();
const summaryData = ref({});
const props = defineProps(["selectedItems", "startDate", "endDate", "loadingMessage"]);

const toCamelCase = (str) =>
  str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

const processEntries = (items) => {
  const result = {};
  for (const item of items) {
    const isPrevious = item.period === "previous";

    for (const [key, value] of Object.entries(item)) {
      if (key === "period") continue;

      const camelKey = toCamelCase(key);
      const finalKey = isPrevious
        ? `previous${camelKey.charAt(0).toUpperCase()}${camelKey.slice(1)}`
        : camelKey;

      const normalizedValue =
        typeof value === "string" && !isNaN(value.replace(",", "."))
          ? Number(value.replace(",", "."))
          : value;

      result[finalKey] = normalizedValue;
    }
  }

  return result;
};
const fetchSummary = async () => {
  isLoading.value = true;
  try {
    const response = await io.queryAsync(
      {
        identifier: "dashboard.GetDashboardSummary",
        payload: {
          ids: props.selectedItems,
          startDate: props.startDate,
          endDate: props.endDate,
        },
      },
      true
    );

    if (!isComponentMounted) return; // Prevent state updates if unmounted

    if (!response) {
      console.error("Summary response is null or undefined:", response);
      return;
    }

    const telemetry = Array.isArray(response.telemetry)
      ? processEntries(response.telemetry)
      : {};
    const widgets = Array.isArray(response.widgets)
      ? processEntries(response.widgets)
      : {};

    summaryData.value = { ...telemetry, ...widgets };
    console.log("Processed summaryData", summaryData.value);
  } catch (error) {
    console.error("Failed to fetch summary:", error);
  } finally {
    if (isComponentMounted) isLoading.value = false;
  }
};

onMounted(() => {
  isComponentMounted = true;
  fetchSummary();
});

watch([
  () => props.selectedItems,
  () => props.startDate,
  () => props.endDate
], fetchSummary);

const metricOrder = [
  "totalApiRequests",
  "totalAiRequests",
  "totalBatchRequests",
  "totalSignins",
  "totalRenders",
  "averageRequestsPerDay",
  "averageRequestDuration",
  "avgSessionsPerDay",
  "averageEngagementTime",
  "averageRendersPerUser",
  "topRoomType",
  "topStyle",
];

const hasData = computed(() => {
  return Object.keys(summaryData.value).length > 0;
});

const filteredSummaryData = computed(() => {
  return metricOrder
    .map((key) => ({
      key,
      value: summaryData.value[key],
    }))
    .filter((item) => item.value !== undefined);
});

const isNumeric = (value) => typeof value === "number";

const getPreviousValue = (key) => {
  const prevKey = `previous${key.charAt(0).toUpperCase()}${key.slice(1)}`;
  return summaryData.value[prevKey];
};

const getAbsoluteChange = (key) => {
  const current = summaryData.value[key];
  const previous = getPreviousValue(key);
  return isNumeric(current) && isNumeric(previous)
    ? current - previous
    : null;
};

const getPercentageChange = (key) => {
  const current = summaryData.value[key];
  const previous = getPreviousValue(key);
  if (isNumeric(current) && isNumeric(previous) && previous !== 0) {
    return (((current - previous) / previous) * 100).toFixed(1);
  }
  return null;
};

const formatChange = (key) => {
  const current = summaryData.value[key];
  const previous = getPreviousValue(key);
  if (isNumeric(current) && isNumeric(previous)) {
    const absChange = getAbsoluteChange(key);
    const percent = getPercentageChange(key);
    if (percent === null) return `${formatValue(absChange)} (n/a)`;
    return `${formatValue(absChange)} (${percent}%)`;
  } else if (typeof current === "string" && previous !== undefined) {
    return current === previous ? "No change" : `From: ${previous}`;
  }
  return "";
};

const formatValue = (value, key = "") => {
  if (typeof value === "string") {
    if (
      key.toLowerCase().includes("roomtype") ||
      key.toLowerCase().includes("style")
    ) {
      return formatCamelCase(value);
    }
    return value;
  }

  if (typeof value === "number") {
    if (value >= 1_000_000) return (value / 1_000_000).toFixed(2) + "M";
    if (value >= 1_000) return (value / 1_000).toFixed(2) + "K";
    return value.toFixed(2);
  }

  return value;
};

const getChangeClass = (key) => {
  const current = summaryData.value[key];
  const previous = getPreviousValue(key);
  if (isNumeric(current) && isNumeric(previous)) {
    const delta = current - previous;
    return delta > 0 ? "positive" : delta < 0 ? "negative" : "neutral";
  } else if (typeof current === "string") {
    return current === previous ? "neutral" : "changed";
  }
  return "neutral";
};

const getCategory = (key) => {
  const totals = [
    "totalApiRequests",
    "totalAiRequests",
    "totalBatchRequests",
    "totalSignins",
    "totalRenders",
  ];
  const averages = [
    "averageRequestsPerDay",
    "averageRequestDuration",
    "avgSessionsPerDay",
    "averageEngagementTime",
    "averageRendersPerUser",
  ];
  const tops = ["topRoomType", "topStyle"];

  if (totals.includes(key)) return "totals";
  if (averages.includes(key)) return "averages";
  if (tops.includes(key)) return "tops";
  return "";
};

const formatCamelCase = (text) => {
  if (!text) return "";
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (char) => char.toUpperCase());
};

const formatLabel = (key) => {
  const labels = {
    totalApiRequests: "Total API Requests",
    totalAiRequests: "Total AI Requests",
    totalBatchRequests: "Total Batch Requests",
    averageRequestsPerDay: "Avg. Requests per Day",
    averageRequestDuration: "Avg. Request Duration (ms)",
    avgSessionsPerDay: "Avg. Sessions per Day",
    totalSignins: "Total Widget Sign-Ins",
    totalRenders: "Total Widget Renders",
    averageEngagementTime: "Avg. Widget Engagement Time (min)",
    averageRendersPerUser: "Avg. Widget Renders per User",
    topRoomType: "Top Widget Room Type",
    topStyle: "Top Widget Style",
  };
  return labels[key] || key;
};
</script>


<style scoped>
.dashboard-container {
  padding: 20px;
  background: #f1f3f5;
  border-radius: 8px;
  max-width: 1200px;
  margin: 0 auto;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 20px;
}

.card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
  transition: transform 0.2s;
}
.card:hover {
  transform: translateY(-4px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.metric-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #555;
}
.trend-icon {
  font-size: 1.2rem;
}

.card-body {
  margin-top: 10px;
}
.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}
.metric-change {
  margin-top: 5px;
  font-size: 0.9rem;
  color: #777;
}

.card.totals {
  border-left: 5px solid #007bff;
}
.card.averages {
  border-left: 5px solid #28a745;
}
.card.tops {
  border-left: 5px solid #ffc107;
}

.positive {
  color: #28a745;
}
.negative {
  color: red;
}
.neutral {
  color: #777;
}
.changed {
  color: #ffa500;
}

.loading {
  text-align: center;
  font-size: 1.2rem;
  color: #666;
}

.no-data {
  font-size: 1rem;
  margin-top: 100px;
  color: #000000;
}
</style>
