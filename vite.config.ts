import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
    mode: process.env.NODE_ENV,
    plugins: [vue()],
    define: {
        __INTLIFY_PROD_DEVTOOLS__: false,
    },
    resolve: {
        alias: {
            '@': '/src',
            '@/*': '/src/*',
        }
    },
    server: {
      port: 5512,
      hmr: true,
      allowedHosts: true
    },
    build: {
        target: 'esnext',
        sourcemap: true
    }
});
