<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { Chart as ChartJS, BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue";

ChartJS.register(BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);
const io = useIO();

const chartData = ref({ labels: [], datasets: [] });
const isLoading = ref(false);
const isMounted = ref(true);

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    title: { display: true, text: "Average Duration per Endpoint (ms)" },
  },
  scales: {
    x: { title: { display: true, text: "Endpoint" }, ticks: { autoSkip: false, maxRotation: 45 } },
    y: { title: { display: true, text: "Duration (ms)" } },
  },
};

const fetchData = async () => {
  isLoading.value = true;
  try {
    const response = await io.queryAsync({
      identifier: "dashboard.getAverageDurationPerEndpoint",
      payload: {},
    }, true);

    if (isMounted.value) {
      chartData.value = {
        labels: response.map(entry => entry.endpoint),
        datasets: [
          {
            label: "Avg Duration (ms)",
            data: response.map(entry => entry.averageDuration),
            backgroundColor: "rgba(255, 99, 132, 0.5)",
            borderColor: "rgba(255, 99, 132, 1)",
            borderWidth: 1,
          },
        ],
      };
    }
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    if (isMounted.value) {
      isLoading.value = false;
    }
  }
};

onMounted(fetchData);

onUnmounted(() => {
  isMounted.value = false;
});
</script>

<template>
  <div class="chart-container">
    <Loader v-if="isLoading" />
    <Bar v-else-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
    <p v-else>No data available</p>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
