<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { useIO, IQueryList } from "@proptexx-com/io";
import { useSidebar, useToast } from '@proptexx-com/vue';
import ManageProfileComp from './manage-profile.vue';
import ManageEmailComp from './manage-email.vue';
import ManagePhoneComp from './manage-phone.vue';
import ManagePasswordComp from './manage-password.vue';
import { toDateStr } from '@/providers/date';
import { update, updates } from '@/providers/workspace-updater';
import CreateWorkspaceComp from '@/components/create-workspace.vue';
import DateTime from '@/components/datetime.vue';
import { useRoute } from 'vue-router';
import cardActions from '@/components/card-actions.vue';
import Loader from "@/components/loader.vue"; // Import Loader component

const sb = useSidebar();
const toast = useToast();
const route = useRoute();
const io = useIO();
const result = ref();
const profileLoading = ref(false); // Separate loading state for profile section
const workspacesLoading = ref(false); // Separate loading state for workspaces section
const initialLoading = ref(true); // State to track initial loading

let stopUpdatesWatcher: (() => void) | null = null; // Store watcher cleanup function

const q: IQueryList = {
    profile: { identifier: 'account.GetProfile', payload: {} },
    phones: { identifier: 'account.GetPhones', payload: {} },
    emails: { identifier: 'account.GetEmails', payload: {} },
    workspaces: { identifier: 'account.GetWorkspaces', payload: {} },
};

async function fetchData() {
    initialLoading.value = true; // Set initial loading to true
    try {
        result.value = await io.queryManyAsync(q, true);
    } finally {
        initialLoading.value = false; // Ensure initial loading is set to false
    }
}

async function refreshProfile() {
    profileLoading.value = true; // Set loading to true for profile section
    try {
        result.value.profile = await io.queryAsync(q.profile, true);
        result.value.emails = await io.queryAsync(q.emails, true);
        result.value.phones = await io.queryAsync(q.phones, true);
    } finally {
        profileLoading.value = false; // Ensure loading is set to false
    }
}

async function refreshWorkspaces() {
    workspacesLoading.value = true; // Set loading to true for workspaces section
    try {
        result.value.workspaces = await io.queryAsync(q.workspaces, true);
    } finally {
        workspacesLoading.value = false; // Ensure loading is set to false
    }
}

stopUpdatesWatcher = watch(() => updates.value, async () => {
    if (route.meta.action) {
        if (route.meta.action === 'set-password') {
            onPasswordClick();
        }
    }
    await fetchData(); // Use fetchData function
}, { immediate: true });

async function createWorkspace() {
    const feedback = await sb.open('New workspace', CreateWorkspaceComp);
    if (feedback) update();
}

async function onUpdateClick(profile: any) {
    if (!result.value?.profile?.id) return;
    const feedback = await sb.open('Update Profile', ManageProfileComp, { profile: result.value.profile });
    if (feedback === true) {
        await io.authAsync({});
        update();
    }
}

async function onPasswordClick() {
    const props = { accountId: io.session?.$accountId };
    sb.open('Change Password', ManagePasswordComp, props);
}

async function onEmailClick(item?: any) {
    if (!result.value?.profile?.id) return;
    const accountId = result.value.profile.id;
    const props = { accountId, item };
    const feedback = await sb.open('Email', ManageEmailComp, props);
    update();
}

async function onPhoneClick(item?: any) {
    if (!result.value?.profile?.id) return;
    const accountId = result.value.profile.id;
    const props = { accountId, item };
    const feedback = await sb.open('Phone', ManagePhoneComp, props);
    update();
}

async function selectSub(sub: any) {
    if (!sub.id) return;

    await io.authAsync({
        workspace: { workspaceId: sub.id }
    });
}

async function disconnectWithGoogle() {
    const response = await io.executeAsync({
        identifier: 'account.OAuthDisconnect',
        payload: { provider: 'google' }
    });

    if (response?.isSuccess) {
        toast.success('Single Sign-On', `Disconnected your account from Google`);
        update();
    }
}

function gender(g?: number) {
    if (g === 1) return 'Female';
    if (g === 2) return 'Male';
    return '';
}

onMounted(() => {
    fetchData(); // Fetch data on component mount
});

onUnmounted(() => {
    result.value = null; // Clear result to prevent memory leaks
    if (stopUpdatesWatcher) {
        stopUpdatesWatcher(); // Stop the watcher to prevent memory leaks
        stopUpdatesWatcher = null;
    }
});
</script>

<template>
    <section>
        <Loader v-if="initialLoading" /> <!-- Display Loader during initial loading -->

        <div v-else>
            <!-- Profile Section -->
            <div class="card">
                <Loader v-if="profileLoading" /> <!-- Display Loader for profile section -->
                <div v-if="!profileLoading">
                    <div class="card-header">
                        <h3 v-if="result?.profile">{{ result.profile.firstName }} {{ result.profile.familyName }}</h3>
                        <card-actions :items="[
                            { title: 'Update Profile', icon: 'fa fa-edit', onClick: () => onUpdateClick(result.profile) },
                            { title: 'Change Password', icon: 'fa fa-key', onClick: () => onPasswordClick() },
                            { title: '_Refresh', icon: 'fa fa-refresh', onClick: () => refreshProfile() }, // Refresh profile section
                        ]"></card-actions>
                    </div>

                    <div class="card-body">
                        <dl v-if="result?.profile">
                            <template v-if="io.session?.isRoot === 'true'">
                                <dt>Id</dt>
                                <dd>{{ result.profile.id }}</dd>
                            </template>

                            <dt>First Name</dt>
                            <dd>{{ result.profile.firstName }}</dd>
                            <dt>Family Name</dt>
                            <dd>{{ result.profile.familyName }}</dd>

                            <template v-if="result.profile.gender">
                                <dt>Gender</dt>
                                <dd>{{ gender(result.profile.gender) }}</dd>
                            </template>

                            <template v-if="result.profile.dateOfBirth">
                                <dt>Date of birth</dt>
                                <dd>{{ toDateStr(result.profile.dateOfBirth) }}</dd>
                            </template>

                            <dt>Member Since</dt>
                            <dd><DateTime :value="result.profile.createdAt"></DateTime></dd>

                            <template v-if="result.profile.emails && result.profile.emails.length > 0">
                                <dt>Emails</dt>
                                <dd class="py-1 space-x-2">
                                    <span :class="item.verifiedAt ? 'bg-green-200': 'bg-red-200'"
                                            class="p-1 rounded cursor-pointer" 
                                            v-for="item of result.profile.emails" 
                                            :key="item.id" 
                                            @click="() => onEmailClick(item)">
                                        {{ item.email }}
                                    </span>
                                </dd>
                            </template>

                            <template v-if="result.profile.phones && result.profile.phones.length > 0">
                                <dt>Phones</dt>
                                <dd class="py-1 space-x-2">
                                    <span :class="item.verifiedAt ? 'bg-green-200': 'bg-red-200'"
                                            class="p-1 rounded cursor-pointer"
                                            v-for="item of result.profile.phones"
                                            :key="item.id"
                                            @click="() => onPhoneClick(item)">
                                        {{ item.number }}
                                    </span>
                                </dd>
                            </template>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Workspaces Section -->
            <div class="card">
                <Loader v-if="workspacesLoading" /> <!-- Display Loader for workspaces section -->
                <div v-if="!workspacesLoading">
                    <div class="card-header">
                        <h3>Workspaces</h3>
                        <card-actions :items="[
                            { title: 'New workspace', icon: 'fa fa-edit', onClick: () => createWorkspace() },
                            { title: '_Refresh', icon: 'fa fa-refresh', onClick: () => refreshWorkspaces() }, // Refresh workspaces section
                        ]"></card-actions>
                    </div>

                    <div class="card-body">
                        <div v-if="result?.workspaces && result.workspaces.length > 0" class="overflow-x-auto">
                            <table class="table is-striped">
                                <thead>
                                    <tr>
                                        <td>Name</td>
                                        <td>Roles</td>
                                        <td>&nbsp;</td> 
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr v-for="ws of result.workspaces" :key="ws.id">
                                        <td>
                                            <span class="link" @click.prevent="selectSub(ws)">
                                                {{ ws.title }}
                                            </span>
                                        </td>
                                        <td>{{ ws.roles }}</td>
                                        <td class="w-[15px]">
                                            <i class="fa fa-star fa-2xs text-red-800" v-if="io.session?.workspaceId === ws.id"></i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div v-else>
                            <p>No workspaces</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>