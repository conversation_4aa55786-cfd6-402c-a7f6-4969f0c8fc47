<script setup>
import { ref, onMounted, watchEffect } from "vue";
import { Chart as ChartJS, BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";

ChartJS.register(BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);
const io = useIO();

const chartData = ref({
  labels: [],
  datasets: [
    {
      label: "Avg Time Spent (minutes)",
      data: [],
      backgroundColor: "rgba(75, 192, 192, 0.7)",
      borderColor: "rgba(75, 192, 192, 1)",
      borderWidth: 1,
    },
  ],
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: true },
    title: { display: true, text: "Average Time Spent per Active User (Daily)" },
  },
  scales: {
    x: { title: { display: true, text: "Date" }, grid: { display: false } },
    y: { title: { display: true, text: "Minutes" }, beginAtZero: true },
  },
};

const fetchData = async () => {
  try {
    const response = await io.queryAsync({
      identifier: "dashboard.getUserTimeSpentPerDay",
      payload: {}, // Backend should filter for last 7 days
    }, true);

    console.log("Raw API Response:", response);

    if (!Array.isArray(response) || response.length === 0) {
      console.warn("No data received for user time spent.");
      return;
    }

    const labels = [];
    const data = [];

    response.forEach(entry => {
      if (entry.date) {
        labels.push(entry.date.split("T")[0]);
        data.push(entry.totalMinutes ?? 0);
      }
    });

    chartData.value = {
      labels,
      datasets: [
        {
          label: "Avg Time Spent (minutes)",
          data,
          backgroundColor: "rgba(75, 192, 192, 0.7)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1,
        },
      ],
    };

    console.log("Updated Chart Data:", chartData.value);
  } catch (error) {
    console.error("Error fetching time spent data:", error);
  }
};

// Ensure chart updates when data changes
watchEffect(() => {
  console.log("ChartData Updated:", chartData.value);
});

onMounted(fetchData);
</script>

<template>
  <div class="chart-container">
    <Bar v-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
    <p v-else>No data available</p>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}
</style>
