<script setup lang="ts">
import { useModal } from '@proptexx-com/vue';

const {message} = defineProps<{
    message: string
}>();

const modal = useModal();
</script>

<template>

    <div class="flex flex-col justify-between">

        <div class="pb-6">{{ message }}</div>

        <div class="flex flex-row space-x-2">

            <button type="button" class="btn is-primary flex-1" @click="() => modal.close(true)">
                Confirm
            </button>

            <button type="button" class="btn is-error flex-1" @click="() => modal.close(false)">
                Cancel
            </button>

        </div>

    </div>

</template>