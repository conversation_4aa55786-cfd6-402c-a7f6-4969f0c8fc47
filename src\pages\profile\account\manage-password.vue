<script setup lang="ts">
import { Form, Val, FormObject } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';
import { onMounted } from 'vue';

const { accountId } = defineProps<{
    accountId: string
}>();

const sb = useSidebar();
const toast = useToast();
const io = useIO();

const form = Form.object({
    password: Form.string({
        title: 'New password',
        format: 'input:password',
        validators: [
            Val.isRequired, 
            Val.minLength(6)
        ]
    }),
    confirmPassword: Form.string({
        format: 'input:password',
        validators: [Val.isRequired, Val.minLength(6), Val.custom((entry) => {
            const passInput = entry?.parent?.e('password') as FormObject;
            if (!passInput) return null;

            return (entry.value !== passInput.value)
                ? 'Must match with password'
                : null;
        })]
    })
});

onMounted(() => {
    if (!accountId){
        toast.error('Error', 'Unknown account');
        return sb.close(false);
    }
});

async function submit()
{
    if (form.invalid) return;

    const response = await io.executeAsync({
        identifier: 'account.ChangePassword',
        payload: form.value
    });

    if (!response?.isSuccess)
    {
        toast.error('Error', response?.errorMessage || 'An error occured');
        return;
    }

    toast.success('Success', 'Your password has changed');
    sb.close(true);
}

</script>

<template>

    <section>
        <n-form :form="form" @submit.prevent="submit">
            <div class="space-x-2">
                <button type="submit" class="btn is-primary">
                    <i class="fa fa-check"></i>
                    Submit
                </button>
                <button type="button" class="btn" @click="() => sb.close()">
                    <i class="fa fa-close"></i>
                    Close
                </button>
            </div>
        </n-form>
    </section>

</template>