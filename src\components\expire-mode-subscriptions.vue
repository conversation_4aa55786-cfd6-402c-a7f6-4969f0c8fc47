<script setup lang="ts">
import { reactive, watch } from 'vue';
import { useToast } from '@proptexx-com/vue';

const toast = useToast();

const props = defineProps({
  mode: String,
  quota: Number,
  specific: String,
  durationPeriod: Object,
  today: String
});
const emit = defineEmits(['update']);

const state = reactive({
  mode: props.mode,
  quota: props.quota,
  specific: props.specific,
  durationAmount: props.durationPeriod?.amount ?? 1,
  durationUnit: props.durationPeriod?.unit ?? 'day'
});

watch(() => props.mode, v => state.mode = v);
watch(() => props.quota, v => state.quota = v);
watch(() => props.specific, v => state.specific = v);
watch(() => props.durationPeriod, v => {
  state.durationAmount = v?.amount ?? 1;
  state.durationUnit = v?.unit ?? 'day';
});

function validate() {
  const today = props.today ?? new Date().toISOString().slice(0, 10);
  if (state.mode === 'specific') {
    if (typeof state.quota !== 'number' || state.quota < 1) {
      toast.error('Limit Request must be a positive number');
      return false;
    }
    if (state.specific && state.specific < today) {
      toast.error('Specific Date cannot be in the past');
      return false;
    }
  } else if (state.mode === 'duration-period') {
    if (typeof state.durationAmount !== 'number' || state.durationAmount < 1) {
      toast.error('Amount must be a positive number');
      return false;
    }
  }
  return true;
}

let lastEmit = '';
watch(state, () => {
  if (!validate()) return;
  const payload = {
    mode: state.mode,
    quota: state.quota,
    specific: state.specific,
    durationPeriod: state.mode === 'duration-period' ? { amount: state.durationAmount, unit: state.durationUnit } : undefined
  };
  const str = JSON.stringify(payload);
  if (str !== lastEmit) {
    emit('update', payload);
    lastEmit = str;
  }
}, { immediate: true, deep: true });
</script>

<template>
  <div class="flex flex-col gap-2 w-full">
      <div class="flex flex-col w-full">
        <label class="option-label mb-1">Quota (number of usages):</label>
        <input type="number" min="1" v-model.number="state.quota" class="number-input w-full" placeholder="Enter quota (e.g. 1000)" />
      </div>
    <div class="flex flex-row gap-4 mb-2">

      <label class="option-label">Expiry Date Mode:</label>
      <label>
        <input type="radio" value="specific" v-model="state.mode" /> Specific Date
      </label>
      <label>
        <input type="radio" value="duration-period" v-model="state.mode" /> Duration
      </label>
    </div>
    <div v-if="state.mode === 'specific'" class="flex flex-col gap-2 w-full">
      <div class="flex flex-col w-full">
        
        <input type="date" v-model="state.specific" :min="today" class="date-input w-full" />
      </div>
      <!-- <div class="flex flex-col w-full">
        <label class="option-label mb-1">Limit Request (number of uses):</label>
        <input type="number" min="1" v-model.number="state.quota" class="number-input w-full" placeholder="Enter quota (e.g. 1000)" />
      </div> -->
    </div>
    <div v-else class="flex flex-row w-full gap-4 items-end">
      <div class="flex flex-col flex-1">
        <label class="option-label mb-1">Amount</label>
        <input type="number" min="1" v-model.number="state.durationAmount" class="number-input input-wide" />
      </div>
      <div class="flex flex-col flex-1">
        <label class="option-label mb-1">Unit</label>
        <select v-model="state.durationUnit" class="select-input input-wide">
          <option value="day">Day</option>
          <option value="month">Month</option>
          <option value="year">Year</option>
        </select>
      </div>
    </div>
  </div>
</template>
<style scoped>
.option-label {
  font-weight: 500;
  margin-right: 0.5rem;
  min-width: 120px;
}
.select-input,
.number-input,
.date-input {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
  height: 38px;
  box-sizing: border-box;
}
.number-input {
  width: 100px;
}
.input-wide {
  width: 100%;
  min-width: 120px;
  max-width: 220px;
}
.input-error {
  border-color: #dc2626;
}
.error-message {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}
</style>
