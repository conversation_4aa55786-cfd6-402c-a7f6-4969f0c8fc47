<script setup lang="ts">
import { update, updates } from '@/providers/workspace-updater';
import { useSidebar } from '@proptexx-com/vue';
import SubmitRequestComp from './submit.vue';

const sb = useSidebar();

const q = {
    identifier: 'workspace.GetBatches',
    payload: {}
};

function getStatusText(status: number)
{
    if (status === -2) return 'Error'
    if (status === -1) return 'Denied'
    if (status === 0) return 'Received'
    if (status === 1) return 'In Progress'
    if (status === 2) return 'Completed'
}

async function submitRequest()
{
    await sb.open('Submit Request', SubmitRequestComp);
}

</script>

<template>

    <section class="card">

        <div class="card-header">
            <div>
                <h3>Requests</h3>
            </div>
            <div>
                <!-- <button type="button" @click="() => submitRequest()">
                    <i class="fa fa-edit"></i>
                    Submit Request
                </button>
                <span class="vertical-separator"></span> -->
                <button type="button" @click="() => update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">

            <n-query :config="q" :key="updates" #="items">
    
                <template v-if="items.length > 0">
                    <div class="overflow-x-auto">
                        <n-table :data="items" #="x">
                            <span v-if="x.col === 1">
                                {{ getStatusText(x.value) }}
                            </span>
                            <span v-else>{{ x.value }}</span>
                        </n-table>
                    </div>
                </template>
    
                <template v-else>
                    <p>No requests have been submitted</p>
                </template>
    
            </n-query>

        </div>

    </section>

</template>