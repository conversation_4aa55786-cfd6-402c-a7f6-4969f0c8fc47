<script setup lang="ts">
import { ref } from 'vue';
import { IQuery } from '@proptexx-com/io';
import Loader from "@/components/loader.vue";

const updates = ref(0);
const isLoading = ref(false);

const q: IQuery = {
    identifier: 'system.getServices',
    payload: {}
};

function handleQueryStart() {
    isLoading.value = true;
}

function handleQueryEnd() {
    isLoading.value = false;
}
</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>Services</h3>
                <div>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">

                <Loader v-if="isLoading" />

                <n-query 
                    :config="q" 
                    :key="updates" 
                    @start="handleQueryStart" 
                    @end="handleQueryEnd" 
                    #="result"
                >

                    <div class="overflow-x-auto" v-if="!isLoading">

                        <template v-if="result && result.length">
                            <table class="table is-striped">
                                <thead>
                                    <tr>
                                        <th class="w-[22px]"></th>
                                        <th>Service</th>
                                        <th class="!text-center">Type of Service</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item of result" :key="item.id">
                                        <td class="!text-center">
                                            <i class="fa fa-play text-green-500"></i>
                                        </td>
                                        <td>
                                            <router-link :to="{name: 'service', params: { id: item.id }}">
                                                {{ item.title }}
                                            </router-link>
                                            <small class="block max-sm:hidden">{{ item.description }}</small>
                                        </td>
                                        <td class="text-center">{{ item.serviceType }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </template>

                        <p v-else class="text-center text-gray-500">No data available</p>

                    </div>

                </n-query>

            </div>

        </div>

    </section>

</template>