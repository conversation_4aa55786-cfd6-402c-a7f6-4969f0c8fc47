<script setup lang="ts">
import { useIO } from "@proptexx-com/io";
import { useSidebar } from '@proptexx-com/vue';
import { updates, update } from '@/providers/workspace-updater';
import AddFilter from './add.vue';
import DateTime from '@/components/datetime.vue';

const io = useIO();
const sb = useSidebar();

const q = {
    identifier: 'workspace.getWidgetUrls',
    payload: {}
};

async function addFilter(){
    const feedback = await sb.open('Add URL Filter', AddFilter);
    if (feedback) update();
}

function toMatchingStrategy(value: number)
{
    if (value === 0) return 'Start match';
    if (value === 1) return 'Exact match';
    if (value === 2) return 'Regex match';
    return '-';
}

</script>

<template>

    <section class="card">

        <div class="card-header">
            <div>
                <h3>URL Filters</h3>
            </div>
            <div>
                <button type="button" @click="() => addFilter()">
                    <i class="fa fa-edit"></i>
                    Add Filter
                </button>
                <span class="vertical-separator"></span>
                <button type="button" @click="() => update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">

            <n-query :config="q" :key="updates" #="items">
    
                <template v-if="items.length > 0">

                    <div class="overflow-x-auto">

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>URL Filter</th>
                                    <th>Matching Strategy</th>
                                    <th class="!text-right">Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item of items">
                                    <td>{{ item.urlFilter }}</td>
                                    <td>{{ toMatchingStrategy(item.matchStrategy) }}</td>
                                    <td class="text-right"><DateTime :value="item.createdAt"></DateTime></td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                </template>
    
                <template v-else>
                    <p>No URL filters have been registered and as a consequence the widget can be used from any website - granted they have your widget key</p>
                    <p>We strongly encourage you to setup at least one URL filter to prevent this from happening</p>
                    <p class="pt-5">
                        <button type="button" class="link" @click="addFilter">
                            <i class="fa fa-edit"></i>
                            Add URL filter
                        </button>
                    </p>
                </template>
    
            </n-query>

        </div>

    </section>

</template>