<script setup lang="ts">
import { useContextMenu } from '@proptexx-com/vue';
import { ContactManager } from './manager';
import { AccountModel, ContactModel } from './types';

const cm = useContextMenu();
const { manager } = defineProps<{
    manager: ContactManager
}>();

const emits = defineEmits(['change']);

function add(a: AccountModel, c: ContactModel)
{
    manager.toggle(a, c);
    cm.close(true);
}

</script>

<template>

    <section class="flex flex-col max-h-96 overflow-y-auto">

        <div v-if="manager.searchResult && manager.searchResult.length > 0">

            <div class="p-1 mb-2 rounded-sm bg-slate-100" v-for="a of manager.searchResult" :key="a.id">

                <h3 class="text-sm border-b pb-1 mb-1">
                    {{ a.fullName }}
                </h3>

                <div class="py-0.5 text-xs cursor-pointer" v-for="c of a.contacts" :key="c.id" @click="add(a, c)">

                    <i class="fa text-pt-red-500 mx-1 text-sm" :class="c.type === 'phone' ? 'fa-phone' : 'fa-envelope'"></i>
                    <span>{{ c.value }}</span>

                </div>
    
            </div>

        </div>

    </section>

</template>