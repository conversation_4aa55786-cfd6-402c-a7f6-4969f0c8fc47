<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
import { Chart as ChartJ<PERSON>, LineController, LineElement, PointElement, LinearScale, CategoryScale, Title, Tooltip, Legend } from "chart.js";
import { Line } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue"; // Import Loader component

ChartJS.register(LineController, LineElement, PointElement, LinearScale, CategoryScale, Title, Tooltip, Legend);

const props = defineProps(["selectedItem", "startDate", "endDate"]); // ✅ Receive props

const io = useIO();
const loading = ref(true);
const error = ref(null);

const chartData = ref({
  labels: [],
  datasets: [{ label: "Active Users", data: [], borderColor: "rgba(54, 162, 235, 1)", backgroundColor: "rgba(54, 162, 235, 0.2)", fill: true }]
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: { legend: { display: true }, title: { display: true, text: "Active Users (Last 7 Days)" } },
  scales: { x: { title: { display: true, text: "Date" } }, y: { title: { display: true, text: "Users" }, beginAtZero: true } }
};

const fetchData = async () => {
  loading.value = true;
  error.value = null;
  try {
    console.log("Fetching active users...");
    let id = null;
    let userType = null;

    if (props.selectedItem !== null) {
      [id, userType] = props.selectedItem.split("|");
    }

    const response = await io.queryAsync(
      { identifier: "dashboard.getActiveUsers", payload: { id, userType, startDate: props.startDate, endDate: props.endDate } },
      true
    );

    console.log("API Response:", response);

    if (!Array.isArray(response) || response.length === 0) {
      chartData.value = { labels: [], datasets: [{ label: "Active Users", data: [] }] };
      return;
    }

    chartData.value = {
      labels: response.map(entry => entry.usageDate.split("T")[0]),
      datasets: [{ label: "Active Users", data: response.map(entry => entry.activeUsers ?? 0), borderColor: "rgba(54, 162, 235, 1)", backgroundColor: "rgba(54, 162, 235, 0.2)", fill: true }]
    };
  } catch (err) {
    error.value = "Error fetching active users data.";
    console.error(error.value, err);
  } finally {
    loading.value = false;
  }
};

// Watch for prop changes and update data
watch(() => [props.selectedItem, props.startDate, props.endDate], fetchData, { immediate: true });

onMounted(fetchData);
</script>

<template>
  <div class="chart-container">
    <Loader v-if="loading" /> <!-- Use Loader component -->
    <p v-else-if="error" class="error">{{ error }}</p>
    <Line v-else-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
    <p v-else>No data available</p>
  </div>
</template>

<style scoped>
.chart-container { width: 100%; height: 450px; display: flex; flex-direction: column; justify-content: center; align-items: center; }
.error { color: red; font-weight: bold; }
</style>
