import { Component, VNode, h, resolveDynamicComponent } from "vue";

export interface DynamicComponentDef{
    component: Component | HTMLElementTagNameMap | string;
    attr?: Record<string, any>;
    children?: DynamicComponentDef[];
}

export function DynamicComponent({config}: { config: DynamicComponentDef })
{
    let component: any;
    let children: any;
    
    if (typeof config.component === 'function')
    {
        // component = Promise.resolve().then(() => config.component());
        // children = () => loadChildren(config.children);
    }
    else if (typeof config.component === 'object')
    {
        component = config.component;
        children = () => loadChildren(config.children);
    }
    else
    {
        component = config.component || 'div';
        children = loadChildren(config.children);
    }

    return h(component, config.attr || {}, children);
}

function loadChildren(children?: DynamicComponentDef[]): VNode[] | undefined
{
    if (!Array.isArray(children)) return undefined;
    return children.map(x => DynamicComponent({config: x}));
}