<script setup lang="ts">
import CopyToClipboard from '@/components/copy-to-clipboard.vue';
import { useIO } from '@proptexx-com/io';
import { computed, onMounted, ref } from 'vue';
import Loader from '@/components/loader.vue';

const apiKey = ref();
const embeddedString = computed(() => `&lt;script src="https://static.proptexx.com/widget/loader.js?k=${apiKey.value}"&gt;&lt;/script&gt;`);
const io = useIO();

onMounted(async () => {
    const response = await io.queryAsync({
        identifier: 'workspace.getClients',
        payload: { serviceType: 'widget' }
    }, true)

    if (!Array.isArray(response) || response.length <= 0) return;
    apiKey.value = response[0].apiKey;
});

</script>

<template>

    <div class="card">

        <div class="card-header">
            <div>
                <h3>Get started with the Proptexx Widget</h3>
            </div>
        </div>

        <div class="card-body widget-guide">

            <p>
                Thank you for choosing our innovative widget designed to transform your website into a dynamic hub for
                interior design innovation. Our widget uses computer vision and regenerative AI models to refurnish
                interior pictures and collect lead information from your users
            </p>

            <h3>Key Features</h3>
            <ul>
                <li>
                    <strong>Computer Vision</strong><br>
                    Automatically recognize elements in interior photos to suggest
                    realistic furnishing upgrades.
                </li>
                <li>
                    <strong>Regenerative AI Models</strong><br>
                    Use AI to reimagine and render interior designs in user-uploaded photographs.
                </li>
                <li>
                    <strong>Lead Generation</strong>
                    Efficiently collect information from users interacting with the widget, helping you to follow up on potential customer leads.
                </li>
            </ul>

            <h3>Installation Instructions</h3>
            <ol>
                <li>
                    <strong>Include the JavaScript Library:</strong>
                    <p>Add the following script tag into the <span class="code">&lt;head&gt;</span> 
                        section of your HTML document to load our widget:</p>
                    <template v-if="apiKey">
                        <CopyToClipboard :text="embeddedString"></CopyToClipboard>
                    </template>
                    <template v-else>
                        <Loader></Loader>
                    </template>
                </li>
                <!-- <li>
                    <strong>Activate the Widget:</strong>
                    <p>Add the following script where you want the widget to appear:</p>
                    <code class="code">
                        &lt;script&gt;
                        window.onload = function() {
                            ProptexxWidget.init({
                                // Optional settings can be added here
                                option1: value1,
                                option2: value2
                            });
                        }
                        &lt;/script&gt;
                    </code>
                </li> -->
            </ol>

            <h3>Support</h3>
            <p>If you need any assistance during installation or have questions about customization, our dedicated
                support team is ready to assist you. Please feel free to contact us at [Support Email/Phone].</p>

            <p>Thank you for choosing our widget. We look forward to seeing it in action on your site!</p>

        </div>

    </div>

</template>

<style lang="postcss" scoped>
.widget-guide {

    @apply dark:text-gray-400;

    h3{
        @apply !pt-5;
    }

    p {
        @apply mt-4 mb-4 leading-relaxed;
    }

    ul {
        @apply mb-5;

        li{
            @apply py-2;
        }
    }
}
</style>