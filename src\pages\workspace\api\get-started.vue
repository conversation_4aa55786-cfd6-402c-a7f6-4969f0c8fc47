<script setup lang="ts">
</script>

<template>

    <div class="card">
        
        <div class="card-header">
            <div>
                <h3>Get started with Proptexx APIs</h3>
            </div>
        </div>

        <div class="card-body api-guide">

            <p>Our REST API allows you to integrate our advanced computer vision and regenerative AI capabilities directly into your applications. This API enables you to refurnish interior pictures and gather user data programmatically, providing a flexible and powerful way to enhance your software solutions.</p>

            <h3>Key Features</h3>
            <ul class="space-y-3 mb-5">
                <li>
                    <strong>Computer Vision</strong><br>
                    Automatically analyze and suggest realistic furnishing upgrades in interior photos.
                </li>
                <li>
                    <strong>Regenerative AI Models</strong><br>
                    Use AI to generate and render new interior design suggestions based on user-submitted images.
                </li>
                <li>
                    <strong>Data Collection</strong><br>
                    Efficiently gather and analyze user data from API interactions to drive insights and business decisions.
                </li>
            </ul>

            <h3>Getting Started with the API</h3>
            <ol class="space-y-3 mb-5">
                <li>
                    <strong>Set Up Your Access Keys:</strong>
                    <p>Log into your account dashboard.</p>
                    <p>Navigate to the 'API Access' section.</p>
                    <p>Click on 'Create New Key' to generate a new API key.</p>
                    <p>Note down your API Key; you will need it to authenticate your API requests.</p>
                </li>
                <li>
                    <strong>Make Your First API Request:</strong>
                    <p>Here’s an example of how to make a request to analyze an image:</p>
                    <pre><code style="white-space: pre-wrap">
curl -X POST https://api.example.com/analyze \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -H 'Content-Type: application/json' \
    -d '{
        "image_url": "https://example.com/image.jpg"
    }'
                    </code></pre>
                </li>
                <li>
                    <strong>Monitor Your Requests:</strong>
                    <p>Go back to your dashboard and select the 'Usage Statistics' section.</p>
                    <p>Here you can view detailed logs of all API requests made, including timestamps, response statuses, and quotas.</p>
                </li>
            </ol>

            <h3>Handling API Responses:</h3>
            <p>Here's an example of a successful API response:</p>
            <pre><code style="white-space: pre-wrap">
{
    "status": "success",
    "data": {
        "suggestions": [
            {"item": "Sofa", "design": "Modern", "color": "Grey"},
            {"item": "Coffee Table", "design": "Contemporary", "color": "Black"}
        ]
    }
}
            </code></pre>

            <h3>Security and Privacy:</h3>
            <p>We take your security and privacy seriously. All API communications are encrypted using HTTPS, and your data is handled according to our privacy policy.</p>

        </div>

    </div>
    
</template>

<style lang="postcss" scoped>
.api-guide {

    @apply dark:text-gray-400;

    h3{
        @apply !pt-5;
    }

    p {
        @apply leading-relaxed;
    }

    ul {
        @apply mb-5;

        li{
            @apply py-2;
        }
    }
}
</style>