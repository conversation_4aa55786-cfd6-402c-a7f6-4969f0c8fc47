<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue"; // Import Loader component

const io = useIO();

// Props
const props = defineProps({
  modelValue: {
    type: [Array, String, Number, Object, Boolean],
    default: () => [], // Initialize with an empty array if not provided
  },
});

// Emit updated value
const emit = defineEmits(["update:modelValue"]);

// Local state to hold the combo options and loading state
const comboOptions = ref([]);
const isLoading = ref(true); // Track loading state

// Deep clone utility function
const deepClone = (obj) => JSON.parse(JSON.stringify(obj));

// Fetch data when the component is mounted
onMounted(async () => {
  try {
    isLoading.value = true; // Set loading to true
    const response = await io.queryAsync(
      { identifier: "dashboard.getChartComboOptionsV2", payload: {} },
      true
    );

    console.log("Backend Response:", response);

    if (response && response.children) {
      comboOptions.value = deepClone(response);
    } else {
      console.error("Invalid response structure:", response);
    }

    console.log("Processed Combo Options:", comboOptions.value);
  } catch (error) {
    console.error("Error fetching combo options:", error);
  } finally {
    isLoading.value = false; // Set loading to false
  }
});

// Handle the selection change
const handleSelectionChange = (item, isSelected) => {
  const updatedModelValue = Array.isArray(props.modelValue) ? [...props.modelValue] : [];
  if (isSelected) {
    updatedModelValue.push(item);
  } else {
    const index = updatedModelValue.findIndex((i) => i.id === item.id);
    if (index !== -1) {
      updatedModelValue.splice(index, 1);
    }
  }
  emit("update:modelValue", updatedModelValue); // Emit updated value
};

// Watch comboOptions to see if it changes properly
const unwatchComboOptions = watch(comboOptions, (newValue) => {
  console.log("Combo options updated:", newValue);
});

// Cleanup watchers to prevent memory leaks
onBeforeUnmount(() => {
  unwatchComboOptions();
});
</script>

<template>
  <div class="tree-selector">
    <!-- Display Loader component if loading -->
    <Loader v-if="isLoading" />

    <!-- Display the tree view once comboOptions is populated -->
    <div v-else class="tree-view">
      <!-- Root-level options (Account) -->
      <div v-for="(option, index) in comboOptions" :key="index">
        <input
          type="checkbox"
          :value="option.id"
          :checked="Array.isArray(props.modelValue) && props.modelValue.some((i) => i.id === option.id)"
          @change="(event) => handleSelectionChange(option, event.target.checked)"
        />
        <span>{{ option.name }}</span>

        <!-- Recursively render workspaces and clients -->
        <div v-if="option.children && option.children.length > 0" style="margin-left: 20px;">
          <div v-for="(workspace, workspaceIndex) in option.children" :key="workspaceIndex">
            <input
              type="checkbox"
              :value="workspace.id"
              :checked="Array.isArray(props.modelValue) && props.modelValue.some((i) => i.id === workspace.id)"
              @change="(event) => handleSelectionChange(workspace, event.target.checked)"
            />
            <span>{{ workspace.name }}</span>

            <div v-if="workspace.children && workspace.children.length > 0" style="margin-left: 20px;">
              <div v-for="(client, clientIndex) in workspace.children" :key="clientIndex">
                <input
                  type="checkbox"
                  :value="client.id"
                  :checked="Array.isArray(props.modelValue) && props.modelValue.some((i) => i.id === client.id)"
                  @change="(event) => handleSelectionChange(client, event.target.checked)"
                />
                <span>{{ client.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tree-selector {
  margin-bottom: 20px;
  max-width: 400px;
}

.tree-view {
  display: flex;
  flex-direction: column;
}

.tree-node {
  margin-bottom: 10px;
}

.tree-node input {
  margin-right: 5px;
}

.children {
  margin-left: 20px;
}
</style>
