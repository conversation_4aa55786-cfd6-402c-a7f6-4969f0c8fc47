<script setup>
import { ref, watch, defineProps, computed, onMounted, onUnmounted } from "vue";
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import ClientSelector from "@/components/dashboard/clientSelector.vue";
import Loader from "../loader.vue"; // Import Loader component

ChartJS.register(BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);
const io = useIO();

// Props from parent
const props = defineProps(["selectedItems", "startDate", "endDate"]);

// Store all fetched data
const allData = ref([]);
const selectedFilterValues = ref([]);
const isLoading = ref(false); // Track loading state
let isComponentMounted = false; // Ensure proper initialization

// Chart data and options
const chartData = ref({
  labels: [],
  datasets: [],
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  indexAxis: 'y',
  plugins: {
    legend: { position: 'top' },
    title: { display: true, text: "Model Requests by Workspace" },
    tooltip: {
      callbacks: {
        label: function (tooltipItem) {
          const dataset = tooltipItem.dataset;
          const index = tooltipItem.dataIndex;
          return `${dataset.label}: ${dataset.data[index]}`;
        },
      },
    },
  },
  scales: {
    x: { stacked: true },
    y: { stacked: true }
  }
};

// Fetch data from API
const fetchData = async () => {
  isLoading.value = true;
  try {
    const response = await io.queryAsync({
      identifier: "dashboard.getModelsPerAccountId",
      payload: {
        ids: props.selectedItems,
        startDate: props.startDate,
        endDate: props.endDate
      },
    }, true);

    if (!isComponentMounted) return; // Prevent state updates if unmounted

    allData.value = response;
    updateChartData();
  } catch (error) {
    console.error("Error fetching request data:", error);
  } finally {
    if (isComponentMounted) isLoading.value = false; // Ensure loading state is updated only if mounted
  }
};

// Computed property to filter data based on selected values dynamically
const filteredData = computed(() => {
  const filterProperty = "endpoint";
  return selectedFilterValues.value.length > 0
    ? allData.value.filter(entry => selectedFilterValues.value.includes(entry[filterProperty]))
    : allData.value;
});

// Dynamically calculate the height of the chart container
const chartHeight = computed(() => {
  const numItems = filteredData.value.length;
  const itemHeight = 30;
  const minHeight = 400;
  return Math.max(minHeight, numItems * itemHeight);
});

// Update chart based on filtered data
const updateChartData = () => {
  const workspaces = [...new Set(filteredData.value.map(entry => entry.workspaceName))];
  const endpoints = [...new Set(filteredData.value.map(entry => entry.endpoint))];

  // Assign colors to workspaces
  const workspaceColors = {};
  const colors = ["#FFB6C1", "#B3E5FC", "#FFEB3B", "#A5D6A7", "#FFE082", "#80DEEA", "#FFCDD2", "#D1C4E9", "#FFCC80"];

  workspaces.forEach((workspace, index) => {
    workspaceColors[workspace] = colors[index % colors.length];
  });

  // Create datasets, one per workspace
  const datasets = workspaces.map(workspace => ({
    label: workspace,
    backgroundColor: workspaceColors[workspace],
    data: endpoints.map(endpoint => {
      const entry = filteredData.value.find(e => e.workspaceName === workspace && e.endpoint === endpoint);
      return entry ? entry.requestCount : 0;
    }),
  }));

  chartData.value = { labels: endpoints, datasets };
};

// Watch for changes
watch([
  () => props.selectedItems,
  () => props.startDate,
  () => props.endDate
], fetchData, { deep: true });
watch(selectedFilterValues, updateChartData);

onMounted(() => {
  isComponentMounted = true; // Set component as mounted
  fetchData();
});

onUnmounted(() => {
  isComponentMounted = false; // Prevent memory leaks by stopping updates
});
</script>

<template>
 <div class="chart-container" :style="{ height: chartHeight + 'px' }">
    <div class="loader-container" v-if="isLoading"> <!-- Center loader -->
      <Loader />
    </div>
    <div v-else class="content-container"> <!-- Add consistent spacing -->
      <!-- Client Selector for filtering by dynamic property (endpoint) -->
      <ClientSelector :clients="allData" v-model:selectedClients="selectedFilterValues" filterProperty="endpoint" />

      <!-- Chart -->
      <div :style="{ height: chartHeight - 83 + 'px' }">
        <Bar v-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
        <p v-else class="no-data">No data available</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%; /* Adjust to take full height */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* Ensure loader takes up full height */
}

.content-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px; /* Add spacing between elements */
}
</style>
