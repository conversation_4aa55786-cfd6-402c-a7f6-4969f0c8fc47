<script setup lang="ts">
import CopyToClipboard from '@/components/copy-to-clipboard.vue';
import { IQuery, useIO } from '@proptexx-com/io';
import { onMounted, ref } from 'vue';
import Loader from '@/components/loader.vue';

const apiKey = ref();
const io = useIO();

onMounted(async () => {
    const response = await io.queryAsync({
        identifier: 'workspace.getClients',
        payload: { serviceType: 'widget' }
    }, true)

    if (!Array.isArray(response) || response.length <= 0) return;
    apiKey.value = response[0].apiKey;
});

</script>

<template>

    <router-view v-if="$route.name !== 'widget'"></router-view>

    <div v-else class="card">

        <div class="card-header">
            <div>
                <h3>Widget</h3>
            </div>
        </div>

        <div class="card-body">

            <h4>Widget API Key</h4>
            <div v-if="apiKey" class="pb-2">
                <CopyToClipboard :text="apiKey"></CopyToClipboard>
            </div>
            <Loader v-else></Loader>

        </div>

        <hr>

        <div class="card-body">

            <ol class="space-y-4">
                <li>
                    <router-link :to="{ name: 'widget-get-started' }">
                        <h4>Get started</h4>
                    </router-link>
                    <p>An introduction on how to get the widget up and running. This section provides step-by-step instructions for installation and initial configuration, ensuring users can quickly begin utilizing the widget.</p>
                </li>
                <li>
                    <router-link :to="{ name: 'url-filters' }">
                        <h4>URL Filters</h4>
                    </router-link>
                    <p>Register URL filters to control where your widget is displayed. This is a recommended security feature that helps prevent unwanted usage outside of your domains</p>
                </li>
                <li>
                    <router-link :to="{ name: 'listings' }">
                        <h4>Listings</h4>
                    </router-link>
                    <p>Track and analyze the usage of the widget across different pages. This section provides a detailed log that includes the absolute URLs where the widget has been opened, the number of images scraped and processed from each listing and other relevant details of each request. Use this data to monitor widget performance and optimize its deployment across various sites</p>
                </li>
                <li>
                    <router-link :to="{ name: 'leads' }">
                        <h4>Leads</h4>
                    </router-link>
                    <p>Manage user registrations collected through the widget. This section offers a detailed list of leads, including full names and email addresses of users who have registered via the widget. Utilize this information to build and nurture your customer base, facilitating effective communication and engagement strategies.</p>
                </li>
                <li>
                    <a href="https://docs.proptexx.com/widget">
                        <h4>Documentation <i class="fa fa-external-link fa-xs ml-1"></i></h4>
                    </a>
                    <p>Comprehensive resource for detailed information about the widget. This section includes all necessary technical documentation, user manuals, and FAQs to help you understand and troubleshoot any aspect of the widget.</p>
                </li>
            </ol>

        </div>

    </div>

</template>
