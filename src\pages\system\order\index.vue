<script setup lang="ts">
import { IQuery } from '@proptexx-com/io';
import { ref } from 'vue';
import DateTime from '@/components/datetime.vue';
import Money from '@/components/money.vue';

const updates = ref(0);
const q: IQuery = {
    identifier: 'system.getOrders',
    payload: {}
}
</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>Orders</h3>
                <div>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">

                <n-query :config="q" :key="updates" #="items">

                    <div v-if="items && items.length > 0" class="overflow-x-auto">
                        
                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Purchased by</th>
                                    <th>Paid at</th>
                                    <th class="w-[85px]">Price</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of items">
                                    <td>
                                        {{ item.productTitle }}
                                    </td>
                                    <td>
                                        {{ item.workspaceName }}<br>
                                        <small>{{ item.referencePerson }}</small>
                                    </td>
                                    <td>
                                        <span v-if="item.paidAt" class="p-1 rounded bg-green-200">
                                            <DateTime :value="item.paidAt"></DateTime>
                                        </span>
                                        <span v-else class="p-1 rounded bg-red-200">
                                            Pending
                                        </span>
                                    </td>
                                    <td><Money :amount="item.priceAmount" :currency="item.currency"></Money></td>
                                </tr>
                            </tbody>

                        </table>

                    </div>

                    <div v-else class="flex justify-center items-center h-full">
                        <p>No data available</p>
                    </div>

                </n-query>

            </div>

        </div>

    </section>

</template>