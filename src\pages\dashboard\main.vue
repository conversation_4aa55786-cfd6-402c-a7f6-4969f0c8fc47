<script setup>
import SummaryContainer from "@/components/dashboard/summaryContainer.vue";
import { isRoot } from "@/providers/utils";
</script>

<template>
  <div v-if="isRoot">
    <SummaryContainer />
  </div>

  <!-- Access Denied Message -->
  <div v-else class="access-denied">
    <h1>Access Denied</h1>
    <p>You do not have permission to view this page.</p>
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
  height: auto;
  padding: 20px;
  background: #f1f3f5;
  border-radius: 8px;
}

.filters {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tabs button {
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    background-color: #ddd;
    border-radius: 5px;
}

.tabs button.active {
    background-color: #007bff;
    color: white;
}

.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tabs button {
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    background-color: #ddd;
    border-radius: 5px;
}

.tabs button.active {
    background-color: #007bff;
    color: white;
}

.charts {
  width: 100%;
}

/* Access Denied Styling */
.access-denied {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    color: red;
}

.access-denied h1 {
  font-size: 2rem;
  margin-bottom: 10px;
}
/* Media Queries để tùy chỉnh chiều cao */
@media (max-width: 1200px) {
  .dashboard-container {
    height: 85vh; /* Chiều cao nhỏ hơn cho màn hình trung bình */
  }
  .access-denied {
    height: 90vh;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    height: 80vh; /* Chiều cao nhỏ hơn cho màn hình nhỏ */
  }
  .access-denied {
    height: 85vh;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    height: auto; /* Tự động điều chỉnh chiều cao cho màn hình rất nhỏ */
    padding: 10px;
  }
  .access-denied {
    height: auto;
    padding: 10px;
  }
}
</style>
