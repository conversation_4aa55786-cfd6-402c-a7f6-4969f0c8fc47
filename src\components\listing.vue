<script setup lang="ts">
import { IQuery, useIO } from '@proptexx-com/io';
import { ActionLink } from '@/components/action-bar/types';
import { onMounted, ref } from 'vue';
import { FormEntry } from '@proptexx-com/forms';

export interface ListingConfig {
    queryProvider: (payload: any, lastItem?: any) => IQuery;
    onClick?: (item: any) => Promise<void>;
    actions?: ActionLink[];
    cols?: string[];
    searchForm?: FormEntry;
    back?: () => void | Promise<void>;
}

const props = defineProps<ListingConfig>();
const io = useIO();
const _response = ref<any[]>([]);
const _lastCount = ref(0);
const _totalCount = ref(-1);
const _updateKey = ref(0);
const isLoaded = ref(false);
let _lastItem: any;
let _append = true;

onMounted(_load);

async function _onClick(item: any): Promise<void> {
    if (typeof props.onClick === 'function') {
        await props.onClick(item);
        return;
    }
}

async function _load() {
    const payload = typeof props.searchForm === 'object'
        ? { ...props.searchForm.value }
        : {};

    const query = props.queryProvider(payload, _lastItem);
    const response = await io.queryAsync(query, true);

    if (response) isLoaded.value = true;

    if (!_append) _response.value.splice(0);
    _response.value.push(...response);

    _lastCount.value = response.length;
    _totalCount.value = _response.value.length;
    _updateKey.value++;
}

function _loadMore() {
    _append = true;
    _lastItem = _response.value[_response.value.length - 1];
    return _load();
}

function _submit() {
    _append = false;
    _lastItem = undefined;
    return _load();
}
</script>

<template>

    <section>

        <template v-if="_totalCount > 0">

            <div class="overflow-x-auto">
                <n-table :data="_response" :cols="props.cols" :key="_updateKey" #="{ item, value, col, row }">

                    <span v-if="col === 0" class="link" @click="_onClick(item)">
                        {{ value }}
                    </span>

                    <template v-else>{{ value }}</template>

                </n-table>
            </div>

            <div v-if="_lastCount >= 50" class="px-4 pt-6 text-center">
                <button type="button" class="btn" @click="_loadMore">
                    Load More ..
                </button>
            </div>

        </template>

        <template v-else-if="_totalCount === 0">

            <div>
                <p>No matches</p>
            </div>

        </template>

        <template v-else>

            <div>
                <p>Loading..</p>
            </div>

        </template>
    </section>

</template>

<style scoped lang="postcss">
.sidepane-layout {
    @apply h-full flex flex-col lg:flex-row-reverse lg:items-stretch;

    aside {
        @apply px-2;
        @apply lg:min-w-[225px];

        .actions {
            @apply flex flex-wrap;
            @apply flex-row py-2 space-x-4;
            @apply lg:flex-col lg:space-x-0 lg:space-y-1;
        }
    }
}
</style>