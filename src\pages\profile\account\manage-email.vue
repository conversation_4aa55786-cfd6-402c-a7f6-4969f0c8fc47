<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { useIO } from "@proptexx-com/io";
import { useSidebar } from '@proptexx-com/vue';

const { accountId, item } = defineProps<{
    accountId: string,
    item?: any,
}>();

const io = useIO();
const sb = useSidebar();
const isNew = typeof item === 'undefined';

const form = Form.object({
    accountId: Form.string({ title: null, format: 'input:hidden' }),
    emailAddress: Form.string({
        validators: [Val.isRequired, Val.email],
        readonly: !isNew,
        description: item && !item.verifiedAt ? 'Email address is not verified' : undefined
    })
});

form.setValue({ accountId, emailAddress: item?.email })

async function onSubmit()
{
    // const response = await io.executeAsync({
    //     identifier: 'account.addPhone',
    //     payload: form.value
    // });

    // console.log(response);
}

</script>

<template>

    <section>
        <n-form :form="form" @submit.prevent="onSubmit">
            <div class="space-x-2">
                <button v-if="isNew" type="submit" class="btn is-primary">
                    Add Email
                </button>

                <button v-if="!isNew && item && !item.verifiedAt" type="button" class="btn is-primary" @click="() => sb.close()">
                    Send verification email
                </button>
                <button type="button" class="btn" @click="() => sb.close()">
                    Close
                </button>
            </div>
        </n-form>
    </section>

</template>