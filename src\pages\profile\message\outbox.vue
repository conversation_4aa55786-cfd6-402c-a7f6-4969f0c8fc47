<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import { IQuery } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import DateTime from '@/components/datetime.vue';
import OutboxMessageComp from './outbox-message.vue';

const props = defineProps<{ searchQuery: string }>(); //query from index.vue
const sb = useSidebar();
const updates = ref(0);

const q: IQuery = {
    identifier: 'message.getOutbox',
    payload: {}
};

async function view(message: any){
    const title = message.subject.length > 20 
        ? message.subject.substr(0, 20) + '..'
        : message.subject;

    await sb.open(title, OutboxMessageComp, { message });
}

// Computed para filtrar os resultados
const filteredResults = computed(() => (result: any[]) => {
    const query = props.searchQuery.toLowerCase();
    return result.filter(msg => 
        msg.subject.toLowerCase().includes(query) || 
        msg.recipients.toLowerCase().includes(query)
    );
});
</script>

<template>
    <section>
        <div class="card">
            <div class="card-header">
                <button type="button" @click="updates++">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>

            <div class="card-body">
                <n-query :config="q" :key="updates" #="result">
                    <div v-if="filteredResults(result).length > 0" class="overflow-x-auto">
                        <table class="min-w-full bg-white border rounded-lg">
                            <thead>
                                <tr class="bg-gray-200 text-left">
                                    <th class="p-3">Inbox</th>
                                    <th class="p-3">To</th>
                                    <th class="p-3">Received</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="msg in filteredResults(result)" :key="msg.id" class="border-b hover:bg-gray-100">
                                    <td class="p-3">
                                        <button type="button" class="text-blue-600" @click="view(msg)">
                                            {{ msg.subject }}
                                        </button>
                                    </td>
                                    <td class="p-3">{{ msg.recipients }}</td>
                                <td class="p-3 text-gray-500">
                                    <DateTime :value="msg.createdAt" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                    
                    <div v-else class="text-center">
                        <p>Your outbox is empty</p>
                                            </div>

                </n-query>
            </div>

        </div>

    </section>

</template>