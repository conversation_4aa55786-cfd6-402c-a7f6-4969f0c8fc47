<script setup lang="ts">
import { Form } from '@proptexx-com/forms';
import { ContactManager } from './manager';
import {debounce} from '@/providers/utils';
import { useContextMenu } from '@proptexx-com/vue';
import ContactResultComponent from './result.vue';
import { watch, toRaw } from 'vue';

const { modelValue } = defineProps<{
    modelValue: any[]
}>();

const emit = defineEmits(['update:modelValue']);
const cm = useContextMenu();
const rawValue = toRaw(modelValue);
const manager = new ContactManager(rawValue);

watch(() => manager.updates.value, () => {
    emit('update:modelValue', manager.selections);
});

const form = Form.string({
    title: null,
    placeholder: 'Search here..',
    value: manager.searchValue,
    onFocus: showContextResult,
    onKeyup: debounce(async (ev: Event) =>
    {
        const searchVal = form.value as string;
        await manager.searchAsync(searchVal);
        showContextResult(ev);

    }, 500)
});

async function showContextResult(ev: Event)
{
    if (!manager.searchResult || manager.searchResult.length <= 0)
    {
        cm.close();
        return;
    }

    const target = ev.target as HTMLElement;

    cm.open({
        attachTo: {
            element: target,
            relativePlacement: 'left-bottom',
            relativeWidth: true
        },
        component: {
            type: ContactResultComponent,
            props: { manager }
        }
    });
}

</script>

<template>

    <section>

        <n-form :form="form"></n-form>

        <div :key="manager.updates.value" class="p-0.5 pt-2">

            <div v-if="manager.selections && manager.selections.length > 0">

                <div class="p-1 mb-2 rounded-sm bg-slate-100" v-for="a of manager.selections" :key="a.id">

                    <h3 class="text-sm border-b pb-1 mb-1">
                        {{ a.fullName }}
                    </h3>

                    <div class="flex flex-row justify-between items-center space-x-2 py-0.5 text-xs"
                            v-for="c of a.contacts" 
                            :key="c.id">

                        <div class="flex flex-row items-center">
                            <i class="fa text-pt-red-500 mx-1" :class="c.type === 'phone' ? 'fa-phone' : 'fa-envelope'"></i>
                            <span>{{ c.value }}</span>
                        </div>

                        <button type="button" class="link px-1" @click="manager.toggle(a,c)">
                            <i class="fa fa-close"></i>
                        </button>

                    </div>

                </div>

            </div>

        </div>

    </section>

</template>