<script setup>
import { ref, watch, defineProps, computed, onMounted, onUnmounted } from "vue";
import {
  Chart as ChartJ<PERSON>,
  PieController,
  ArcElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";
import { Pie } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue"; // Import Loader component

// Register Chart.js components
ChartJS.register(PieController, ArcElement, Tooltip, Legend, Title);

const io = useIO();
const props = defineProps(["selectedItems", "startDate", "endDate"]);

// Store all fetched data
const allData = ref([]);

// Chart data
const chartData = ref({
  labels: [],
  datasets: [
    {
      label: "Batch Requests",
      data: [],
      backgroundColor: [],
    },
  ],
});

// Loading state
const isLoading = ref(false);

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { position: "bottom" },
    title: { display: true, text: "Batch Requests by Workspace" },
    tooltip: {
      callbacks: {
        label: (tooltipItem) => {
          const dataset = tooltipItem.dataset;
          const index = tooltipItem.dataIndex;
          return `${dataset.label}: ${dataset.data[index]}`;
        },
      },
    },
  },
};

// Fetch data from API
const fetchData = async () => {
  isLoading.value = true; // Set loading state
  try {
    const response = await io.queryAsync(
      {
        identifier: "dashboard.getBatchRequestCount",
        payload: {
          ids: props.selectedItems,
          startDate: props.startDate,
          endDate: props.endDate,
        },
      },
      true
    );

    allData.value = response;
    updateChartData();
  } catch (error) {
    console.error("Error fetching batch request data:", error);
  } finally {
    isLoading.value = false; // Reset loading state
  }
};

// Compute and update chart data
const updateChartData = () => {
  if (!allData.value.length) {
    chartData.value = { labels: [], datasets: [{ data: [], backgroundColor: [] }] };
    return;
  }

  const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40"];

  chartData.value = {
    labels: allData.value.map(entry => entry.workspaceName),
    datasets: [
      {
        label: "Batch Requests",
        backgroundColor: colors.slice(0, allData.value.length),
        data: allData.value.map(entry => entry.batchCount),
      },
    ],
  };
};

// Watch for changes
const stopWatchSelectedItems = watch(() => props.selectedItems, fetchData, { immediate: true });
const stopWatchDates = watch([() => props.startDate, () => props.endDate], fetchData, { deep: true });
watch([
  () => props.selectedItems,
  () => props.startDate,
  () => props.endDate
], fetchData);

// Cleanup watchers to prevent memory leaks
onUnmounted(() => {
  stopWatchSelectedItems();
  stopWatchDates();
});

onMounted(fetchData);
</script>

<template>
  <div class="chart-container">
    <Loader v-if="isLoading" /> <!-- Show loader when loading -->
    <Pie v-else-if="chartData.labels.length" :data="chartData" :options="chartOptions" />
    <p v-else class="no-data">No data available</p>
  </div>
</template>

<style scoped>
.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 400px;
  padding: 16px;
  box-sizing: border-box;
}
.no-data {
  margin-top: -90px;
}
@media (max-width: 768px) {
  .chart-container {
    padding: 8px;
  }
}
</style>
