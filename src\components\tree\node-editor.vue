<script setup lang="ts">
import { useSidebar } from '@proptexx-com/vue';
import { ITreeNode } from './tree-view';

export interface INodeEditorConfig
{
    node: ITreeNode<any>,
    onClick: (name: string, selectedNode: ITreeNode<any>) => Promise<void>
}

const sidebar = useSidebar();
const config: INodeEditorConfig = sidebar.current?.props;

</script>

<template>

    <!-- <section class="vertical-list" v-if="config?.node">

        <div class="p-4 bg-blue-100 border-blue-300 rounded-md" @click="sidebar.close({action: 'addChild', node: config.node})">
            Add Child
        </div>

        <div class="p-4 bg-blue-100 border-blue-300 rounded-md" @click="sidebar.close({action: 'editNode', node: config.node})">
            Edit Node
        </div>

        <div class="p-4 bg-blue-100 border-blue-300 rounded-md" @click="sidebar.close({action: 'removeNode', node: config.node})">
            Remove Node
        </div>

        <div class="p-4 bg-blue-100 border-blue-300 rounded-md" @click="sidebar.close">
            Close
        </div>

    </section>

    <div v-else>
        A config of INodeEditorConfig must be provided
    </div> -->

</template>

<style>
    .vertical-list {
        @apply space-y-1;

        > div{
            @apply p-2 bg-blue-100 hover:bg-blue-200 rounded-sm cursor-pointer;
        }
    }
</style>