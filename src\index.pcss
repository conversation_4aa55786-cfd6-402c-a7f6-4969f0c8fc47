@tailwind base;
@tailwind components;
@tailwind utilities;

html {
    @import url('https://fonts.googleapis.com/css2?family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap');
    font-family: "Urbanist", sans-serif;
    font-optical-sizing: auto;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.5;
    @apply h-full text-gray-900;

    input, select, textarea{
        font-family: "Urbanist", sans-serif;
        font-optical-sizing: auto;
        @apply text-sm;
    }
}

body {
    @apply h-full bg-[#e1e1e1] dark:bg-gray-700 text-gray-900 dark:text-gray-300;
}

#app{
    @apply flex flex-col h-full;

    > header
    {
        @apply flex flex-row justify-between items-center p-2;
        @apply bg-white dark:bg-gray-800 mb-[1px];

        .logo{
            @apply w-[120px] px-3;
        }

        >nav{
            @apply flex flex-row justify-between items-center space-x-2;

            a{
                @apply text-gray-500 text-sm hover:text-primary;
            }

            .credentials{
                @apply rounded-full bg-light hover:bg-primary w-[27px] h-[27px] text-white text-sm;
            }
        }
    }

    > section.page{

        @apply flex-1 flex flex-row justify-around;

        > main
        {
            @apply flex flex-col w-full h-full p-3 md:p-6;
        }

        > aside{
            @apply hidden w-[250px] md:w-[300px] md:flex flex-col bg-white dark:bg-gray-800 p-3 md:p-6;
        }
    }

    > footer
    {
        @apply bg-gray-200 dark:bg-gray-800 text-center p-2 text-sm;

        >div
        {
            @apply container mx-auto;
        }
    }
}

h1, h2, h3, h4, h5 {
    @apply m-0 p-0;
}

h1{
    @apply text-2xl py-1;
}

h2{
    @apply text-xl py-1;
}

h3{
    @apply text-lg;
}

h4{
    @apply text-lg font-bold;
}

h5{
    @apply text-lg;
}

code{
    @apply block p-2 text-sm italic bg-gray-100 dark:bg-gray-800 rounded;
}

a, .link{
    @apply text-left outline-none cursor-pointer text-primary;
}

button.btn{
    @apply inline-block py-1.5 px-2 text-xs bg-primary hover:bg-dark text-white rounded-lg;

    i{
        @apply opacity-80;
    }
}

dl {
    @apply grid grid-rows-2 md:grid-flow-col md:gap-x-3 gap-y-0;

    dt{
        @apply flex flex-col justify-end pt-3 md:pt-0 text-xs align-baseline font-bold;
    }
}

table.table
{
    @apply w-full;

    thead{
        th,td {
            @apply px-1 py-0.5 text-xs font-normal text-left uppercase;
        }
    }

    tbody{
        tr{
            td {
                @apply p-1 py-1.5 text-sm font-normal;
            }
        }
    }

    &.is-striped tbody tr{
        @apply odd:bg-gray-100 dark:odd:bg-transparent;
    }

    &.is-condensed{
        th,td{
            @apply p-0;
        }
    }
}

code, .code {
    @apply rounded bg-gray-100 dark:bg-gray-800 dark:text-white p-2 my-2 whitespace-pre-line overflow-x-auto;
}

input[type=checkbox] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    @apply !w-[14px] !h-[14px] bg-white rounded-sm border-2 border-primary relative; /* Adjusted width and height, added relative positioning */

    &:checked {
        @apply bg-primary;
    }

    &:checked::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 11px;
        height: 7px;
        border-style: solid;
        border-width: 0 1px 1px 0;
        transform: rotate(50deg);
        transform-origin: 4px 6px;
        @apply border-white dark:border-gray-300;
    }
}

input[type=radio] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    @apply w-[14px] h-[14px] bg-white rounded-full border-2 border-primary relative; /* Adjusted width and height, added relative positioning */

    &:checked {
        @apply bg-primary;
    }

    &:checked::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6px;
        height: 8px;
        border-style: solid;
        border-width: 0 1px 1px 0;
        transform: rotate(50deg);
        transform-origin: 4px 6px;
        @apply border-white dark:border-gray-300;
    }
}

/* Base styles for the switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

/* Hide default checkbox */
.toggle-switch input[type=checkbox] {
    opacity: 0;
    width: 0;
    height: 0;
}

/* Custom styles for the label used as the slider */
.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: .4s;
    border-radius: 15px;
    @apply bg-gray-300 dark:bg-gray-600;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 12px;
    width: 12px;
    left: 4px;
    bottom: 4px;
    transition: .4s;
    border-radius: 50%;
    @apply bg-gray-50 dark:bg-gray-400;
}

.toggle-switch input[type=checkbox]:checked + label {
    @apply bg-primary dark:bg-dark;
}

.toggle-switch input[type=checkbox]:checked + label:before {
    transform: translateX(19px);
    @apply bg-gray-50 dark:bg-dark;
}


i.fa{
    @apply min-w-[20px];
}


.logo{
    @apply w-[125px] text-primary dark:text-gray-300;
}

.menu-list {
    @apply space-y-1;

    > div{
        @apply p-1.5 bg-light hover:bg-dark rounded text-white cursor-pointer;
    }
}

.btn
{
    @apply inline-block p-1.5 px-3 rounded-lg bg-gray-600 text-white dark:text-gray-300 hover:bg-opacity-95 active:bg-opacity-90;

    &.is-primary{
        @apply bg-primary hover:bg-opacity-95 active:bg-opacity-90;
    }

    &.is-success{
        @apply bg-green-600 hover:bg-opacity-95 active:bg-opacity-90;
    }

    &.is-warning{
        @apply bg-orange-600 hover:bg-opacity-95 active:bg-opacity-90;
    }

    &.is-error{
        @apply bg-red-700 hover:bg-opacity-95 active:bg-opacity-90;
    }

    &.is-small{
        @apply p-1 px-2 text-sm;
    }

    &[disabled]{
        @apply text-opacity-50 hover:bg-opacity-100 active:bg-opacity-100;
    }
}

.card{
    @apply p-0 mb-4;

    .card-header{
        @apply flex flex-row justify-between py-3 font-bold;

        > div {
            @apply flex flex-row justify-center items-center space-x-1;
        }
    }

    .card-body {
        @apply p-3 py-4 rounded-t-lg last:rounded-b-lg bg-white dark:bg-gray-800;
    }

    .card-footer {
        @apply flex flex-row space-x-4 rounded-b-lg border-t dark:border-t-gray-600 p-3 bg-white dark:bg-gray-800;
    }
}

fieldset{
    @apply flex flex-col box-border;

    fieldset{
        @apply flex-row space-x-2;
    }

    legend{
        @apply w-full mb-2 pt-2 pb-1 border-b;
    }
}

.form-control
{
    @apply flex flex-col pb-4 box-border;

    label {
        @apply pt-1 pb-2 text-base capitalize;
    }

    input, textarea{
        @apply w-full p-2 box-border border border-gray-200 dark:border-gray-700 outline-none bg-gray-50 dark:bg-gray-800 focus:border-primary dark:focus:border-white rounded-lg;
    }

    label.checkbox{
        @apply flex flex-row py-1.5 select-none;
        input[type=checkbox], input[type=radio]{
            @apply mr-2;
        }
    }

    select{
        @apply w-full p-2 border outline-none border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-lg;
    }

    &.otp-control{
        display: flex;
        flex-direction: row;
        justify-content: center;
    }
    
    &.otp-control > :not([hidden]) ~ :not([hidden]){
        --tw-space-x-reverse: 0;
        margin-right: calc(1rem * var(--tw-space-x-reverse));
        margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
    }
    
    &.otp-control{
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    
    &.otp-control input{
        height: 50px;
        width: 50px;
        text-align: center;
        font-size: 1.25rem;
        line-height: 1.75rem;
        text-transform: uppercase;
    }

    p.hint, p.error{
        @apply text-xs pt-1 pb-2;
    }

    p.error{
        @apply text-red-500;
    }
}

.price-control{
    @apply flex flex-row space-x-2;

    :nth-child(1){
        @apply !w-[100px];
    }

    :nth-child(2){
        @apply !w-[40px];
    }

    :nth-child(3){
        @apply w-full max-w-[150px];
    }
}

.loading-bar
{
    @apply my-4;

    > div{
        @apply w-full h-1 bg-gray-200 relative overflow-hidden;

        > div{
            @apply bg-black absolute top-0 h-full animate-slide-infinite duration-75;
        }
    }
}

.sidebar-header{
    @apply flex flex-row items-center border-b mb-3 p-0.5 pb-2 space-x-2;
}

.tree
{
    @apply relative;

    ul{
        @apply m-0 p-0 list-none;

        ul
        {
            @apply ml-4 hidden;

            &.open{
                @apply block;
            }
        }

        &.open{
            @apply block;
        }
    }

    li{
        @apply list-none select-none;

        > div{
            @apply p-0.5 cursor-pointer;

            &.selected{
                @apply bg-blue-200;
            }
        }
    }

    .toggler {
        @apply w-[18px] p-1 opacity-30;
    }
}

.vertical-separator
{
    @apply w-[1px] h-[20px] bg-gray-100 dark:bg-gray-800;
}

.vertical-list {
    @apply space-y-1;

    > div{
        @apply p-2 bg-blue-100 hover:bg-blue-200 rounded-sm cursor-pointer;
    }
}


nav.main
{
    @apply w-full py-3 dark:text-gray-300 text-sm;

    > div{
        @apply block py-1.5 px-5 border-r-primary text-gray-700;

        &.selected{
            @apply border-r-[3px] font-bold;
        }

        > :first-child { @apply block; }

        .title{
            @apply mt-4 font-normal uppercase text-sm;
        }
    }

    h3{
        @apply pt-2 uppercase font-bold text-sm;
    }

    ul{
        @apply pl-4 space-y-3;

        ul
        {
            @apply py-3 pl-4;
        }

        li{
            @apply cursor-pointer;

            .title{
                @apply mt-1 text-xs uppercase font-normal text-gray-900 dark:text-gray-300;
            }

            a{
                @apply block cursor-pointer dark:text-gray-400;

                &.selected{
                    @apply border-r-2 border-r-primary;
                }

                &.faded{
                    @apply text-opacity-50;
                }
            }
        }
    }
}

nav.sub
{
    @apply rounded-xl border-2 border-white p-5;

    > div{
        @apply block p-3 rounded-xl border-r-primary text-gray-700;

        &.selected{
            @apply bg-gray-400 bg-opacity-20;
        }

        > :first-child { @apply block; }

        .title{
            @apply mt-4 font-normal uppercase text-sm;
        }
    }
}

.container-overflow
{
    @apply w-full overflow-x-auto;
}

.copy-to-clipboard{
    @apply relative;

    code{
        @apply pt-3 pb-2;
    }

    button{
        @apply block pt-0.5 absolute right-2 top-2 bg-transparent bg-opacity-50 text-gray-400 hover:text-primary rounded text-center;
    }
}

.notification
{
    @apply mb-2 p-2 rounded-lg bg-gray-100 text-sm pb-3;

    &.is-success{
        @apply bg-green-100;
    }

    &.is-info{
        @apply bg-blue-100;
    }

    &.is-warning{
        @apply bg-orange-100;
    }

    &.is-error{
        @apply bg-red-100;
    }
}

.panel
{
    &.sidebar{
        @apply fixed top-0 left-0 z-10 w-full h-full flex flex-row justify-end items-end content-end bg-gray-900 bg-opacity-40;

        >.instance{
            @apply flex flex-col w-full md:w-[400px] h-full p-2 bg-white dark:bg-gray-900 shadow shadow-gray-400 dark:shadow-gray-500;

            >.header{
                @apply flex flex-row justify-between p-2 border-b border-b-gray-300 dark:border-b-gray-700;
            }

            >.body{
                @apply h-full overflow-auto p-2;
            }
        }
    }

    &.modal{
        @apply fixed top-0 left-0 z-20 w-full h-full p-2 flex items-center justify-center bg-gray-900 bg-opacity-40;

        >.instance{
            @apply w-[400px] min-h-[20px] border rounded bg-slate-200;

            >.header{
                @apply flex flex-row justify-between p-2 border-b border-b-gray-300;
            }

            >.body{
                @apply p-2;
            }
        }
    }

    &.toast
    {        
        @apply fixed bottom-0 right-1 z-30 w-[300px] h-auto flex flex-col;

        >.instance{
            @apply p-2 mb-1 rounded bg-opacity-40 bg-gray-300;

            &.success{
                @apply bg-green-300 dark:bg-gray-900 dark:text-green-300;
            }

            &.info{
                @apply bg-blue-300 dark:bg-gray-900 dark:text-blue-300;
            }

            &.warning{
                @apply bg-orange-300 dark:bg-gray-900 dark:text-orange-300;
            }

            &.error{
                @apply bg-red-300 dark:bg-gray-900 dark:text-red-300;
            }

            >.header{
                @apply flex flex-row justify-between;
            }
        }
    }

    &.context-menu{
        @apply fixed top-0 left-0 w-full h-full z-40 bg-transparent;

        >.instance{
            @apply absolute w-[200px] min-h-[50px] p-2 mb-1 border rounded bg-white;
        }

        .context-menu-list{

            li{
                @apply p-1 px-2 cursor-pointer hover:bg-gray-200;
            }
        }
    }
}