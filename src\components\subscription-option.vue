<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import ExpireModeSubscriptions from './expire-mode-subscriptions.vue';
import { useIO } from '@proptexx-com/io';
import { sub } from 'date-fns';
import { spec } from 'node:test/reporters';


const props = defineProps<{ modelValue: Record<string, any> }>();
const emit = defineEmits(['update:modelValue']);
const io = useIO();

interface OptionItem {
  value: string;
  label: string;
}
const subscriptionTypes = ref<OptionItem[]>([]);
const loadingOptions = ref(true);

const isDemo = ref(
  typeof props.modelValue.isDemo === 'boolean'
    ? props.modelValue.isDemo
    : false 
);
const subscriptionType = ref('OneTime');
const duration = ref(props.modelValue.duration ?? 1);

// State received from expire-mode-subscriptions
const expireState = ref({
  mode: props.modelValue.expireMode ?? 'expire',
  quota: props.modelValue.quota ?? 1,
  specific: props.modelValue.specific ?? '',
  durationPeriod: props.modelValue.durationPeriod ?? { amount: 1, unit: 'day' }
});

const today = computed(() => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
});

onMounted(async () => {
  try {
    const data = await io.queryAsync<any>({
      identifier: 'workspace.GetSubscriptionSettingOptions',
      payload: {}
    }, true);
    if (data && Array.isArray(data.subscriptionTypes)) {
      subscriptionTypes.value = data.subscriptionTypes.map((item: { id: string; value: string }) => ({
        value: item.id,
        label: item.value
      }));
    } else {
      subscriptionTypes.value = [];
    }
  } catch (err) {
    subscriptionTypes.value = [];
  } finally {
    loadingOptions.value = false;
  }
});

const showQuotaAndExpire = computed(() => ['OneTime', 'PayAsYouGo'].includes(subscriptionType.value));
const showDuration = computed(() => subscriptionType.value === 'Period');
const durationLabel = computed(() => 'Periods');

function handleExpireUpdate(val: any) {
  expireState.value = { ...expireState.value, ...val };
}

watch(
  [isDemo, subscriptionType, duration, expireState],
  () => {
    emit('update:modelValue', {
      isDemo: isDemo.value,
      subscriptionType: subscriptionType.value,
      duration: showDuration.value ? duration.value : undefined,
      quota: showQuotaAndExpire.value ? expireState.value.quota : undefined,
      specific: showQuotaAndExpire.value && expireState.value.mode === 'specific' ? expireState.value.specific : undefined,
      durationPeriod: showQuotaAndExpire.value && expireState.value.mode === 'duration-period' ? expireState.value.durationPeriod : undefined,
      expireMode: expireState.value.mode
    });
  },
  { immediate: true, deep: true }
);
</script>

<template>
  <div class="subscription-options">
    
      <!-- <div class="toggle-container">
        <label class="toggle-env">
          <input
            type="checkbox"
            :checked="environment === 'Test'"
            @change="environment = ($event.target as HTMLInputElement)?.checked ? 'Test' : 'Prod'"
          />
          <span class="toggle-slider" :class="{ active: environment === 'Test' }"></span>
        </label>
      </div> -->
    <div class="option-row">
      <label class="option-label">Demo Mode:</label>
      <div class="toggle-container">
        <label class="toggle-env">
          <input
            type="checkbox"
            v-model="isDemo"
          />
          <span class="toggle-slider" :class="{ active: isDemo }"></span>
        </label>
        <!-- <span class="env-label">{{ isDemo ? 'Dev' : 'Prod' }}</span> -->
      </div>
    </div>
    <div class="option-row">
      <label class="option-label">Subscription Type:</label>
      <select v-model="subscriptionType" class="select-input">
        <option v-for="item in subscriptionTypes" :key="item.value" :value="item.value">{{ item.label }}</option>
      </select>
    </div>
    <ExpireModeSubscriptions
      v-if="showQuotaAndExpire"
      :mode="expireState.mode"
      :quota="expireState.quota"
      :specific="expireState.specific"
      :durationPeriod="expireState.durationPeriod"
      :today="today"
      @update="handleExpireUpdate"
    />
    <div v-if="showDuration" class="option-row">
      <label class="option-label">{{ durationLabel }}:</label>
      <input type="number" min="1" v-model.number="duration" class="number-input" />
    </div>
  </div>
</template>

<style scoped>
.subscription-options {
  padding: 0 0 1rem;
}

.option-row {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.option-group {
  flex-wrap: wrap;
  gap: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
}

.option-label {
  font-weight: 500;
  margin-right: 0.5rem;
  min-width: 120px;
}

.select-input,
.number-input,
.date-input {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
  height: 38px;
  box-sizing: border-box;
}

.number-input {
  width: 100px;
}
.input-wide {
  width: 100%;
  min-width: 120px;
  max-width: 220px;
}

.input-error {
  border-color: #dc2626;
}

.error-message {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Toggle Switch Styles */
.toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-env {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.toggle-env input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  width: 32px;
  height: 18px;
  background: #cbd5e1;
  border-radius: 999px;
  position: relative;
  transition: background 0.2s;
}

.toggle-slider.active {
  background: #1e293b;
}

.toggle-slider::before {
  content: "";
  position: absolute;
  left: 2px;
  top: 2px;
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 50%;
  transition: transform 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.12);
}

.toggle-slider.active::before {
  transform: translateX(14px);
}

.env-label {
  color: #334155;
  font-size: 1rem;
  font-weight: 400;
  min-width: 36px;
}
</style>
