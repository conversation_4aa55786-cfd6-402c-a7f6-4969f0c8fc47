name: Generic Build-Push Azure

on:
  workflow_dispatch:
    inputs:
      branch:
        required: true
        description: "Branch to checkout to it"
        type: string
        default: "master"
      envFile:
        required: true
        type: string
        default: "production"
        description: "Env file to use production , staging"

env:
  docker-registry: proptexx.azurecr.io
  docker-image: proptexx.web.portal
  TAG: ${{ github.sha }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Git Checkout
      uses: actions/checkout@v2
      with:
        ref: ${{ github.event.inputs.branch }}
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    - name: Set short SHA
      run: echo "SHORT_SHA=${GITHUB_SHA::7}" >> $GITHUB_ENV

    - name: Build & Push Docker image
      run: |
        mv .env.${{ github.event.inputs.envFile }} .env
        docker build -t ${{ env.docker-registry }}/${{ env.docker-image }}:${{ env.SHORT_SHA }}  .
        docker login ${{ env.docker-registry }} -u ${{ secrets.DOCKER_USERNAME }} --password-stdin <<<  ${{ secrets.DOCKER_PASSWORD }}
        docker push ${{ env.docker-registry }}/${{ env.docker-image }}:${{ env.SHORT_SHA }} 
