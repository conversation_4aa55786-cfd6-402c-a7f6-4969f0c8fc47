<script setup lang="ts">

const { items } = defineProps<{
    items: ActionItem[]
}>();

export interface ActionItem
{
    title: string,
    icon?: string,
    onClick: Function
}

function onClick(i: number)
{
    items[i].onClick();
};
</script>

<template>

    <section class="menu-list">
        <div v-for="(i, index) of items" :key="index" @click="onClick(index)">
            {{ i.title }}
        </div>
    </section>

</template>
