<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import DateTime from '@/components/datetime.vue';

const route = useRoute();
const updates = ref(0);
const couponId = computed(() => route.params['id'] as string);

function getQuery()
{
    return {
        identifier: 'system.getCoupon',
        payload: { id: couponId.value }
    };
}

function update(){
    updates.value++;
}
</script>

<template>

    <section :key="couponId">

        <router-link :to="{name: 'coupons'}" class="link text-xs !flex !pb-2">
            <i class="fa fa-chevron-left"></i>
            <span>Back to coupons</span>
        </router-link>

        <n-query :config="getQuery" :key="updates" #="{product, orders: products}">

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Coupon</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body min-h-32">

                    <dl>
                        <dt>Code</dt>
                        <dd>{{ product.title }}</dd>
                        <dt>Created At</dt>
                        <dd><DateTime :value="product.createdAt"></DateTime></dd>
                    </dl>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Products</h3>
                    </div>
                    <div>
                        <button type="button">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="products && products.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">
                            <tbody>
                                <tr v-for="product of products" :key="product.id">
                                    <td>{{ product }}</td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Coupon is not binding to any products
                    </div>

                </div>

            </div>

        </n-query>

    </section>

</template>