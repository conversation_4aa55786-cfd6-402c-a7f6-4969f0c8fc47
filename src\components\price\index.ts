import { Form, FormObject, FormObjectConfig } from "@proptexx-com/forms";
import { h } from "vue";
import PriceComp from './price.vue';

export interface PriceConfig extends FormObjectConfig{
    currencies?: Record<string, string> | (() => Record<string, string>) | (() => Promise<Record<string, string>>);
}

export interface PriceValue {
    integer: number,
    fraction?: number,
    currency?: string
}

export interface PriceResult{
    integer: number,
    fraction: number,
    currency: string
}

export function priceControl(value: string, config?: PriceConfig)
{
    const { currencies, ...objectConfig } = config || {};
    return Form.string({
        ...objectConfig,
        value,
        'onUpdate:value': (e: Event) => {
            console.log(e);
        },
        format: (e: any) => {
            return h(PriceComp, {
                value,
                currencies
            });
        }
    });
}