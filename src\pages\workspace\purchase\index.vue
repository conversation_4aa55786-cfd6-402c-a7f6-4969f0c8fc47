<script setup lang="ts">
import { update, updates } from '@/providers/workspace-updater';
import { useSidebar } from '@proptexx-com/vue';
import SubmitBatchComp from './submit.vue';
import DateTime from '@/components/datetime.vue';
import Money from '@/components/money.vue';

const sb = useSidebar();

const q = {
    identifier: 'workspace.GetOrders',
    payload: {}
};

function getStatusText(status: number)
{
    if (status === -2) return 'Error'
    if (status === -1) return 'Denied'
    if (status === 0) return 'Received'
    if (status === 1) return 'In Progress'
    if (status === 2) return 'Completed'
}

async function submitBatch()
{
    await sb.open('Submit Batch', SubmitBatchComp);
}

</script>

<template>

    <section class="card">

        <div class="card-header">
            <div>
                <h3>Orders</h3>
            </div>
            <div>
                <button type="button" @click="() => update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">

            <n-query :config="q" :key="updates" #="items">
    
                <div v-if="items.length > 0" class="overflow-x-auto">
                    
                    <table class="table is-striped">

                        <thead>
                            <tr>
                                <th class="w-[160px]">Product</th>
                                <th class="max-md:hidden">Ordered by</th>
                                <th>Paid At</th>
                                <th class="!text-right">Price</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr v-for="item of items">
                                <td>
                                    {{ item.productTitle }}<br>
                                    <small><DateTime :value="item.createdAt"></DateTime></small>
                                </td>
                                <td class="max-md:hidden">{{ item.referencePerson }}</td>
                                <td>
                                    <span v-if="item.paidAt" class="p-1 rounded bg-green-200">
                                        <DateTime :value="item.paidAt"></DateTime>
                                    </span>
                                    <span v-else class="p-1 rounded bg-red-200">
                                        <a :href="item.paymentLink" target="_blank">
                                            Pending payment. Click to pay
                                        </a>
                                    </span>
                                </td>
                                <td class="text-right">
                                    <Money :amount="item.priceAmount" :currency="item.currency"></Money>
                                </td>
                            </tr>
                        </tbody>

                    </table>

                </div>
    
                <div v-else>
                    <p>No data available</p>
                </div>
    
            </n-query>

        </div>

    </section>

</template>