{"name": "proptexx.portal", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "NODE_ENV=production vite build", "build:dev": "NODE_ENV=development vite build", "preview": "vite preview"}, "dependencies": {"@date-fns/utc": "^2.1.0", "@fortawesome/fontawesome-free": "^6.7.2", "@proptexx-com/forms": "^1.0.2", "@proptexx-com/forms-vue": "^1.0.0", "@proptexx-com/io": "^1.0.1", "@proptexx-com/io-vue": "^1.0.0", "@proptexx-com/vue": "^1.0.1", "@proptexx/widget-sdk": "^3.0.0", "@vueform/multiselect": "^2.6.11", "apexcharts": "^4.7.0", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.2.0", "date-fns": "^4.1.0", "uuid": "^10.0.0", "vue": "^3.5.14", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22.15.18", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}}