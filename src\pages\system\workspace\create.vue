<script setup lang="ts">
import { useIO } from '@proptexx-com/io';
import { Form, Val } from '@proptexx-com/forms';
import { useSidebar, useToast } from '@proptexx-com/vue';
import { phoneValidator, selectList } from '@proptexx-com/forms-vue';

const io = useIO();
const sb = useSidebar();
const toast = useToast();

const form = Form.object({
    name: Form.string({
        title: 'Name of workspace',
        validators: [Val.isRequired, Val.minLength(4)]
    }),
    parentId: selectList(_loadWorkspaces, '', { title: 'Parent workspace'}),
    owner: Form.object({
        fullName: Form.string({
            validators: [Val.isRequired]
        }),
        email: Form.string({
            validators: [Val.isRequired, Val.email]
        }),
    }, { class: 'flex-col'}),
});

async function _loadWorkspaces(): Promise<Record<any, any>>
{
    const response = await io.queryAsync({
        identifier: 'account.GetWorkspaces',
        payload: { }
    }, true);

    if (!Array.isArray(response)) return [];

    return response.map(x => {
        return {
            key: x.id,
            value: `${x.name}`
        };
    });
}

async function submit()
{
    if (form.invalid) return;
    
    const response = await io.executeAsync({
        identifier: 'system.CreateWorkspace',
        payload: form.value
    });
    console.log(response);

    if (!response?.isSuccess)
    {
        toast.error('Error', response?.errorMessage || 'Unable to process');
        return;
    }

    toast.success('Success', 'Workspace has been created');
    sb.close(response.result.workspaceId);
}

</script>

<template>

    <section>

        <div class="sidebar-header">

            <button type="submit" form="sidebarForm" class="link">
                <i class="fa fa-save"></i>
                Save
            </button>

            <span class="vertical-separator"></span>

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>
        
        <n-form :form="form" id="sidebarForm" @submit.prevent="submit">
        </n-form>

    </section>

</template>