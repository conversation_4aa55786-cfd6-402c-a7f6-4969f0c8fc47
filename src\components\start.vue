<script setup lang="ts">
import { useIO } from '@proptexx-com/io';
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const io = useIO();

onMounted(async () => {

    try {
        const response = await fetch(`${import.meta.env.VITE_AUTH_URL}/_refresh`, {
            method: 'POST', // Specify POST method
            credentials: 'include', // Include credentials (HTTP-only cookies)
            headers: {
                'Content-Type': 'application/json', // Set the content type to JSON
            },
            body: JSON.stringify({}) // Sending an empty JSON object as the body
        });

        if (response.ok) {
            const json = await response.json();
            if (typeof json === 'object' && '$accessToken' in json)
            {
                const token = json['$accessToken'];
                console.log(token);
                io.setToken(token);
                return;
            }
        }

    } catch (error) {
    }

    let url = `${import.meta.env.VITE_ACCOUNT_URL}?key=${io.apiKey}`;

    if (window.location.href){
        url += `&redirectUrl=${window.location.href}`;
    }

    window.location.href = url;
});

</script>

<template>

    <section></section>

</template>