<script setup lang="ts">
import { useIO } from '@proptexx-com/io';

const io = useIO();
const redirectTo = io.cache.del('redirectTo') ?? '/';

async function proceed(){

    const response = await io.authAsync({});
    location.href = redirectTo;
}

</script>

<template>

    <section class="px-3">

        <div class="card mx-auto max-w-lg">

            <router-view></router-view>

            <div class="card-footer text-right">
                <button type="button" class="btn is-primary !block text-center" @click="proceed">
                    Proceed
                </button>
            </div>

        </div>

    </section>

</template>