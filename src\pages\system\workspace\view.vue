<script setup lang="ts">
import { computed, h, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DateTime from '@/components/datetime.vue';
import { useSidebar, useToast } from '@proptexx-com/vue';
import AddService from './add-service.vue';
import AddOrder from './add-order.vue';
import AddMember from './add-member.vue';
import ViewApi<PERSON>eyComp from './view-api-key.vue';
import { useIO } from '@proptexx-com/io';
import Money from '@/components/money.vue';
import SummaryContainer from "@/components/dashboard/summaryContainer.vue";
import Loader from "@/components/loader.vue"; // Import Loader component

const route = useRoute();
const router = useRouter();
const io = useIO();
const toast = useToast();
const updates = ref(0);
const sb = useSidebar();
const workspaceId = computed(() => route.params['id'] as string);
const isLoading = ref(false); // Track loading state

function getQueries()
{
    return {
        workspace : {
            identifier: 'system.getWorkspace',
            payload: { id: workspaceId.value }
        },
        members: {
            identifier: 'workspace.getMembers',
            payload: { workspaceId: workspaceId.value }
        },
        orders: {
            identifier: 'workspace.getOrders',
            payload: { workspaceId: workspaceId.value }
        },
        services: {
            identifier: 'workspace.getServices',
            payload: { workspaceId: workspaceId.value }
        },
        clients: {
            identifier: 'workspace.getClients',
            payload: { workspaceId: workspaceId.value }
        }
    };
}

function update(){
    updates.value++;
}

async function addServices()
{
    isLoading.value = true; // Start loading
    try {
        const feedback = await sb.open('Add Services', AddService, { workspaceId: workspaceId.value });
        if (feedback) update();
    } finally {
        isLoading.value = false; // Stop loading
    }
}

async function addOrder(workspaceName: string)
{
    isLoading.value = true; // Start loading
    try {
        const feedback = await sb.open('Add Order', AddOrder, {
            workspaceId: workspaceId.value,
            workspaceName
        });

        if (feedback){
            router.push({ name: 'order', params: { id: feedback }});
        }
    } finally {
        isLoading.value = false; // Stop loading
    }
}

async function addMember(){
    isLoading.value = true; // Start loading
    try {
        const feedback = await sb.open('Add Member', AddMember, { workspaceId: workspaceId.value });
        if (feedback) update();
    } finally {
        isLoading.value = false; // Stop loading
    }
}

async function revokeService(item: any, clients?: any[])
{
    if (!item || !item.id) {
        toast.error('Error', 'Invalid service item');
        return;
    }
    isLoading.value = true;
    try {
        let clientSecretId: string | undefined;

        if (clients && Array.isArray(clients)) {
            let clientName = item.id === 'widget-access' ? 'widget' : item.id;
            const client = clients.find(c => c.name === clientName);
            if (client) clientSecretId = client.secretId;
        } 

        const payload: any = { workspaceId: workspaceId.value, serviceId: item.id };
        if (clientSecretId !== undefined && clientSecretId !== null && clientSecretId !== '') {
            payload.clientSecretId = clientSecretId;
        }
        
        const response = await io.executeAsync({
            identifier: 'workspace.RevokeService',
            payload
        });
        if (!response?.isSuccess)
        {
            toast.error('Error', response?.errorMessage || 'Unable to revoke service');
            return;
        }
        toast.success('Success', `Service revoked`)
        update();
    } finally {
        isLoading.value = false;
    }
}

function onClickApiKey(apiKey?: string)
{
    if (typeof apiKey === 'string')
    {
        return sb.open(`View API key`, ViewApiKeyComp, { apiKey });
    }
}

</script>

<template>

    <section :key="workspaceId">

        <router-link :to="{name: 'workspaces'}" class="link text-xs !flex !pb-2">
            <i class="fa fa-chevron-left"></i>
            <span>Back to workspaces</span>
        </router-link>

        <!-- Loader Component -->
        <Loader v-if="isLoading" />

        <n-query :config="getQueries" :key="updates" #="{workspace, members, orders, services, clients}">

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Workspace</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <dl>
                        <dt>Name</dt>
                        <dd>{{ workspace.name }}</dd>
                        <template v-if="workspace.founderName">
                            <dt>Founder</dt>
                            <dd>{{ workspace.founderName }}</dd>
                        </template>
                        <dt>Created</dt>
                        <dd><DateTime :value="workspace.createdAt"></DateTime></dd>
                    </dl>
                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Members</h3>
                    </div>
                    <div>
                        <button type="button" @click="addMember">
                            <i class="fa fa-edit"></i>
                            Add Member
                        </button>

                        <span class="vertical-separator"></span>

                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="members && members.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th class="w-[175px]">Member Since</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of members" :key="item.id">
                                    <td>
                                        <router-link :to="{name: 'account', params: { id: item.id }}">
                                            {{ item.firstName }} {{ item.familyName }}
                                        </router-link>
                                    </td>
                                    <td>{{ item.emails }}</td>
                                    <td>{{ item.roles }}</td>
                                    <td>
                                        <DateTime :value="item.memberSince"></DateTime>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Workspace has no members
                    </div>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Services</h3>
                    </div>
                    <div>
                        <button type="button" @click="addServices">
                            <i class="fa fa-edit"></i>
                            Add Services
                        </button>

                        <span class="vertical-separator"></span>

                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="services && services.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Since</th>
                                    <th class="w-[175px]">Expires</th>
                                    <th class="w-[35px]">&nbsp;</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item of services" :key="item.id">
                                    <td>{{ item.title }}</td>
                                    <td><DateTime :value="item.hadSince"></DateTime></td>
                                    <td>
                                        <DateTime v-if="item.expiresAt" :value="item.expiresAt"></DateTime>
                                        <span v-else>-</span>
                                    </td>
                                    <td>
                                        <button type="button" class="link" @click="() => revokeService(item, clients)">
                                            <i class="fa fa-close"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Workspace has no services
                    </div>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Orders</h3>
                    </div>
                    <div>
                        <button type="button" @click="() => addOrder(workspace.name)">
                            <i class="fa fa-edit"></i>
                            Add Order
                        </button>

                        <span class="vertical-separator"></span>

                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="orders.result && orders.result.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Placed By</th>
                                    <th>When</th>
                                    <th>Paid At</th>
                                    <th class="w-[100px]">Price</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of orders" :key="item.id">
                                    <td>
                                        <router-link :to="{name: 'order', params: { id: item.orderId }}">
                                            Order #{{ item.orderNbr }}
                                        </router-link>
                                    </td>
                                    <td>{{ item.referencePerson }}</td>
                                    <td><DateTime :value="item.createdAt"></DateTime></td>
                                    <td>
                                        <span v-if="item.paidAt" class="p-1 rounded bg-green-200">
                                            <DateTime :value="item.paidAt"></DateTime>
                                        </span>
                                        <span v-else class="p-1 rounded bg-red-200">
                                            Not paid
                                        </span>
                                    </td>
                                    <td>
                                        <Money :amount="item.priceAmount" :currency="item.currency"></Money>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Workspace has no orders
                    </div>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Clients</h3>
                    </div>
                    <div>
                        <!-- <button type="button" @click="() => onCreateClient()">
                            <i class="fa fa-edit"></i>
                            Add Client
                        </button>

                        <span class="vertical-separator"></span>
                        <span class="vertical-separator"></span> -->

                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">

                    <div v-if="clients && clients.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">

                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Client Name</th>
                                    <th>API Key</th>
                                    <th class="w-[100px]">Created</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of clients" :key="item.id">
                                    <td>
                                        <span>{{ item.name }}</span><br>
                                        {{ item.description }}
                                    </td>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        <button type="button" class="link" @click="() => onClickApiKey(item.apiKey)">
                                            <!-- {{ item.apiKey ? 'View' : 'Add' }} -->
                                            View key
                                        </button>
                                    </td>
                                    <td><DateTime :value="item.createdAt"></DateTime></td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div v-else>
                        Workspace has no clients
                    </div>

                </div>

            </div>

            <div class="card">

                <div class="card-header">
                    <div>
                        <h3>Usage analytics</h3>
                    </div>
                    <div>
                        <button type="button" @click="update">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <SummaryContainer :workspaceId="workspaceId" />
                </div>

            </div>

        </n-query>

    </section>

</template>