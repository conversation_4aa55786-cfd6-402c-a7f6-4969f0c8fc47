<script setup lang="ts">
import { CustomValidatorFunction, Form, FormString, Val } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { useSidebar, useToast } from '@proptexx-com/vue';

const io = useIO();

const urlFilterRegEx = new RegExp(/^(http|https)\:\/\/.+/, 'i');
const urlFilterFn: CustomValidatorFunction = (entry: FormString) =>
{
    const v = entry.value;
    if (typeof v === 'string' && urlFilterRegEx.test(v))
    {
        return undefined;
    }

    return 'An absolute URL is expected';
}

const sb = useSidebar();
const toast = useToast();
const form = Form.object({
    absoluteUrl: Form.string({
        placeholder: 'https://',
        validators: [Val.isRequired, Val.custom(urlFilterFn)],
        description: 'The url starting string you would like to accept - case insensitive'
    })
});

async function submit()
{
    if (form.invalid) return;

    const response = await io.executeAsync({
        identifier: 'workspace.addWidgetUrl',
        payload: form.value
    });

    if (!response?.isSuccess)
    {
        const m = response?.errorMessage || 'Unable to persist URL filter';
        toast.error('Error', m);
        return;
    }

    toast.success('Added', 'URL filter has been added');
    sb.close(true);
}

</script>

<template>

    <n-form :form="form" @submit.prevent="submit">

        <div class="space-x-2">

            <button type="submit" class="btn is-primary">
                Submit
            </button>

            <button type="button" class="link" @click="() => sb.close(false)">
                Cancel
            </button>

        </div>

    </n-form>

</template>