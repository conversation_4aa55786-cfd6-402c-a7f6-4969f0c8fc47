<script setup lang="ts">
import { ActionBarProps } from './types';

const props = defineProps<ActionBarProps>();
</script>

<template>

    <div class="actions" v-if="props.actions && props.actions.length">
        <button v-for="action of props.actions" :key="action.name" class="link" @click="() => action.fn()">
            <i :class="action.icon"></i>
            <span>{{ action.name }}</span>
        </button>
    </div>

</template>

<style lang="postcss" scoped>
.actions{
    @apply space-x-4;

    button > i {
        @apply mr-[2px];
    }
}
</style>