<script setup lang="ts">
import { useSidebar } from '@proptexx-com/vue';
import DateTime from '@/components/datetime.vue';

const sb = useSidebar();
const { message } = defineProps<{
    message: any
}>();

</script>

<template>

    <section>

        <div class="sidebar-header">

            <button type="button" class="link" @click="() => sb.close()">
                <i class="fa fa-close"></i>
                Close
            </button>

        </div>

        <dl class="py-5">
            <dt>Subject</dt>
            <dd>{{ message.subject }}</dd>
            <dt>From</dt>
            <dd>{{ message.sender }}</dd>
            <dt>Received</dt>
            <dd><DateTime :value="message.createdAt"></DateTime></dd>
            <dt>Content</dt>
            <dd>
                <p v-html="message.content"></p>
            </dd>
        </dl>

    </section>

</template>