import { formatDate, formatDistanceToNow } from "date-fns";

function ensureUtcFormat(date: string | number | Date | undefined)
{
    if (typeof date === 'undefined') return undefined;
    
    let result = typeof date !== 'string'
        ? new Date(date).toISOString()
        : date;

    return result.endsWith('Z') ? result : `${result}Z`;
}

export function toDateStr(date: string | number | Date)
{
    const d = ensureUtcFormat(date);
    if (!d) return '';
    return formatDate(d, 'dd MMM yyyy');
}

export function toDateTimeStr(date: string | number | Date)
{
    const d = ensureUtcFormat(date);
    if (!d) return '';
    return formatDate(d, 'dd MMM yyyy @ HH:mm z')
}

export function toTimeAgo(date: string | number | Date)
{
    const d = ensureUtcFormat(date);
    if (!d) return '';
    return formatDistanceToNow(d, { addSuffix: true });
}