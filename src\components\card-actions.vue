<script setup lang="ts">
import { useSidebar } from '@proptexx-com/vue';
import { ref, onMounted, onUnmounted, h } from 'vue';
import MenuList, { ActionItem } from '@/components/menu-list.vue'

const { items, breakpoint } = defineProps<{
    items: (ActionItem | null)[],
    breakpoint?: string
}>();

const breakpoints: Record<string, number> = {
    xs: 0,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
};

const sb = useSidebar();
const viewportWidth = ref(window.innerWidth);

function updateWidth() {
    viewportWidth.value = window.innerWidth;
}

onMounted(() => {
    window.addEventListener('resize', updateWidth);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateWidth);
});

function is(size: string) {
    if (!breakpoints[size]) return false;
    return viewportWidth.value >= breakpoints[size];
}

async function onClickItem(item: ActionItem){
    if (typeof item?.onClick !== 'function') return;
    item.onClick();
}

async function onClickMenu()
{
    let data: ActionItem[] = [];

    for (let i of items){
        if (!i) continue;
        let title = i.title.startsWith('_') ? i.title.substring(1) : i.title;
        data.push({ ...i, title });
    }

    data.push({
        title: 'Close',
        icon: 'fa fa-close',
        onClick: () => sb.closeAll()
    });

    sb.open(`Menu`, MenuList, { items: data });
}
</script>

<template>

    <div>

        <template v-if="is(breakpoint || 'md')">

            <template v-for="item in items">

                <button v-if="item" class="btn" @click="onClickItem(item)">
                    <i v-if="item.icon" :class="item.icon"></i>
                    <span v-if="!item.title.startsWith('_')">{{ item.title }}</span>
                </button>

                <span v-else class="vertical-separator"></span>

            </template>

        </template>

        <button type="button" class="link" :class="`${breakpoint || 'md'}:hidden`" @click="onClickMenu">
            <i class="fa fa-bars"></i>
        </button>

    </div>

</template>

<style>

.card-actions-menu{
    @apply flex flex-col;
}

</style>