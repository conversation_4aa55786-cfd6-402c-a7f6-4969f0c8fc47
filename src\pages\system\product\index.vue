<script setup lang="ts">
import { ref } from 'vue';
import { IQuery } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import CreateProduct from './create.vue';

const sb = useSidebar();
const updates = ref(0);

const q: IQuery = {
    identifier: 'system.getProducts',
    payload: {}
};

async function create()
{
    const feedback = await sb.open('New Product', CreateProduct);
    if (feedback) updates.value++;
}
</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>Products</h3>
                <div>
                    <button type="button" class="link text-sm" @click="create">
                        <i class="fa fa-edit"></i>
                        New Product
                    </button>
                    <span class="vertical-separator"></span>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">

                <n-query :config="q" :key="updates" #="result">

                    <div v-if="result && result.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">
    
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th class="!text-center">Services</th>
                                    <th class="!text-center">Orders</th>
                                    <th class="!text-center">Sales</th>
                                    <th class="!text-right">Price</th>
                                    <th class="!text-right">Total Revenue</th>
                                </tr>
                            </thead>
    
                            <tbody>
                                <tr v-for="item of result" :key="item.id">
                                    <td>
                                        <router-link :to="{name: 'product', params: { id: item.id }}" class="link">
                                            {{ item.title }}
                                        </router-link>
                                        <small class="block max-sm:hidden">{{ item.description }}</small>
                                    </td>
                                    <td class="text-center">{{ item.numServices }}</td>
                                    <td class="text-center">{{ item.numOrders }}</td>
                                    <td class="text-center">{{ item.numSales }}</td>
                                    <td class="text-right">{{ item.price === 1 ? `USD ${item.priceAmount}` : `USD ${item.priceAmount}` }}</td>
                                    <td class="text-right">USD {{ item.totalRevenue }}</td>
                                </tr>
                            </tbody>
    
                        </table>

                    </div>

                    <p v-else>No products</p>

                </n-query>

            </div>

        </div>

    </section>

</template>