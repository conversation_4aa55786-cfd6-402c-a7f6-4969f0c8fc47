<script setup lang="ts">
import { ref } from 'vue';
import { IQuery } from '@proptexx-com/io';
import { useSidebar } from '@proptexx-com/vue';
import CreateCoupon from './create.vue';
import { useRouter } from 'vue-router';
import EditCoupon from './edit.vue';
import { money } from '@/providers/money';

const sb = useSidebar();
const updates = ref(0);
const router = useRouter();

const q: IQuery = {
    identifier: 'system.getCoupons',
    payload: {}
};

async function create()
{
    const feedback = await sb.open('New Coupon', CreateCoupon);
    if (feedback) updates.value++;
}
</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>Coupons</h3>
                <div>
                    <button type="button" class="link text-sm" @click="create">
                        <i class="fa fa-edit"></i>
                        New Coupon
                    </button>
                    <span class="vertical-separator"></span>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">

                <n-query :config="q" :key="updates" #="result">

                    <div v-if="result && result.length > 0" class="overflow-x-auto">

                        <table class="table is-striped">
    
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Discount</th>
                                    <th>Product</th>
                                    <th class="!text-center">Orders</th>
                                    <th class="!text-center">Applied</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="item of result" :key="item.id">
                                    <td>
                                        <router-link :to="{name: 'coupon', params: { id: item.id }}" class="link">
                                            {{ item.code }}
                                        </router-link>
                                    </td>
                                    <td>-{{ item.discountType === 1 ? item.discountValue + '%' : money(item.discountValue, item.currency) }}</td>
                                    <td>{{ item.productTitle || 'Not linked to any products' }}</td>
                                    <td class="text-center">{{ item.numOrders }}</td>
                                    <td class="text-center">{{ item.numApplied }}</td>
                                </tr>
                            </tbody>
    
                        </table>

                    </div>

                    <p v-else>No discount coupons</p>

                </n-query>

            </div>

        </div>

    </section>

</template>