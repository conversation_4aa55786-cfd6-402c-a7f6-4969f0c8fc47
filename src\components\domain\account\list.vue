<script setup lang="ts">
import { update, updates } from '@/providers/workspace-updater';
import DateTime from '@/components/datetime.vue';
import Loader from '@/components/loader.vue'; // Import Loader component
import { useSidebar } from '@proptexx-com/vue';
import { IQuery, useIO } from '@proptexx-com/io';
import { isRoot } from '@/providers/utils';
import { useRouter } from 'vue-router';
import { ref } from 'vue'; // Import ref for reactive state

export interface AccountIndexConfig {
    title: string;
    emptyText: string;
    viewRouteName?: string;
    query: IQuery;
}

const props = defineProps<AccountIndexConfig>();

const io = useIO();
const router = useRouter();
const sb = useSidebar();

const isLoading = ref(false); // Reactive state for loading indicator

async function changeAccount(accountId: string) {
    isLoading.value = true; // Show loader
    try {
        await io.authAsync({
            'change-account': { accountId }
        });
        router.push({ name: 'profile' });
    } finally {
        isLoading.value = false; // Hide loader
    }
}
</script>

<template>
    <section class="card">
        <div class="card-header">
            <div>
                <h3>{{ title }}</h3>
            </div>
            <div>
                <!-- <button type="button" @click="create()">
                    <i class="fa fa-edit fa-sm"></i>
                    Create Account
                </button> -->
                <span class="vertical-separator"></span>
                <button type="button" @click="update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">
            <!-- Display loader when loading -->
            <Loader v-if="isLoading" />

            <n-query :config="query" :key="updates" #="items">
                <div v-if="items && items.length > 0" class="overflow-x-auto">
                    <table class="table is-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th class="w-[175px]">Member Since</th>
                                <th v-if="isRoot(io)" class="w-[100px]">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item of items" :key="item.id">
                                <td>
                                    <router-link v-if="viewRouteName" :to="{ name: viewRouteName, params: { id: item.id }}">
                                        {{ item.firstName }} {{ item.familyName }}
                                    </router-link>
                                    <span v-else>{{ item.firstName }} {{ item.familyName }}</span>
                                </td>
                                <td>{{ item.emails }}</td>
                                <td>
                                    <DateTime :value="item.memberSince"></DateTime>
                                </td>
                                <td v-if="isRoot(io)" class="text-right">
                                    <button type="button" class="link" @click="changeAccount(item.id)">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <template v-else>
                    <p>{{ emptyText }}</p>
                </template>
            </n-query>
        </div>
    </section>
</template>