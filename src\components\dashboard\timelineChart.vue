<template>
  <div class="timeline-chart-container">
    <Loader v-if="isLoading" />
    <!-- Show loader while loading -->
    <div
      v-else-if="!series.length || series.every((s) => !s.data.length)"
      class="no-data"
    >
      No data available
    </div>
    <VueApexCharts
      v-else
      class="vue-apex-charts"
      :key="chartKey"
      :options="chartOptions"
      :series="series"
      type="line"
      height="400"
      ref="chartRef"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, nextTick, onUnmounted } from "vue";
import VueApexCharts from "vue3-apexcharts";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue";

const props = defineProps({
  selectedItems: { type: Array, default: () => [] },
  startDate: { type: String, required: true },
  endDate: { type: String, required: true },
  granularity: { type: String, default: "daily" } // Default granularity is "daily"
});

const io = useIO();
const series = ref([]);
const currentGranularity = ref(props.granularity);
const chartKey = ref(0); // Used to force chart re-render
const chartRef = ref(null);
const isResetting = ref(false); // Flag to avoid zoomed event during reset
const isLoading = ref(false); // Track loading state
let isComponentMounted = true; // Track component mount state

const fetchData = async () => {
  isLoading.value = true;
  try {
    const response = await io.queryAsync(
      {
        identifier: "dashboard.GetRequestsTimeline",
        payload: {
          granularity: currentGranularity.value,
          startDate: props.startDate,
          endDate: props.endDate,
          ids: props.selectedItems.length > 0 ? props.selectedItems : null,
        },
      },
      true
    );

    if (!isComponentMounted) return; // Prevent state updates if unmounted

    series.value = [
      { 
        name: "Total API Requests", 
        data: response.map(d => ({ x: d.timestamp, y: d.totalRequests }))
      },
      { 
        name: "Failed API Requests", 
        data: response.map(d => ({ x: d.timestamp, y: d.failedRequests }))
      },
    ];
  } catch (error) {
    console.error("Error fetching timeline data:", error);
  } finally {
    if (isComponentMounted) isLoading.value = false; // Ensure loading state is updated only if mounted
  }
};

// Reset chart function: resets to default (daily) state.
const resetChart = async () => {
  isResetting.value = true;
  currentGranularity.value = "daily"; // Force default to daily
  await fetchData();
  await nextTick(); // Wait for DOM updates
  chartKey.value++;  // Force full re-render of the chart
  // Update x-axis labels to default daily format
  if (chartRef.value) {
    chartRef.value.updateOptions({
      xaxis: { labels: { format: "dd MMM" } }
    });
  }
  isResetting.value = false;
};

const chartOptions = ref({
  chart: {
    id: "timeline-chart",
    type: "line",
    zoom: {
      enabled: true,
      type: "x",
      autoScaleYaxis: true,
    },
    toolbar: {
      show: true,
      autoSelected: "zoom",
      tools: {
        zoomin: true,
        zoomout: true,
        reset: true,
      },
    },
    events: {
      zoomed: async function (chartContext, { xaxis }) {
        // Do nothing if a reset is in progress.
        if (isResetting.value) return;

        const range = xaxis.max - xaxis.min;
        let newGranularity = "monthly";

        if (range < 1000 * 60 * 60 * 24 * 30) newGranularity = "daily";
        if (range < 1000 * 60 * 60 * 24 * 7) newGranularity = "hourly";
        if (range < 1000 * 60 * 60 * 24) newGranularity = "minute";

        if (newGranularity !== currentGranularity.value) {
          currentGranularity.value = newGranularity;
          await fetchData();
        }

        let newFormat;
        switch (newGranularity) {
          case "monthly": newFormat = "MMM yyyy"; break;
          case "daily": newFormat = "dd MMM"; break;
          case "hourly": newFormat = "HH:mm"; break;
          case "minute": newFormat = "HH:mm:ss"; break;
          default: newFormat = "MMM yyyy";
        }

        if (chartContext && chartContext.updateOptions) {
          chartContext.updateOptions({
            xaxis: { labels: { format: newFormat } },
          });
        }
      },
      beforeResetZoom: async function (chartContext) {
        await resetChart();
        return false; // Prevent default reset behavior
      }
    },
  },
  xaxis: {
    type: "datetime",
    labels: { 
      format: props.granularity === "minute" ? "HH:mm:ss" 
             : props.granularity === "hourly" ? "HH:mm" 
             : props.granularity === "daily" ? "dd MMM" 
             : "MMM yyyy" 
    },
    title: { text: "Time" },
  },
  yaxis: {
    title: { text: "Requests" },
  },
  tooltip: {
    x: { format: "dd MMM yyyy HH:mm" },
  },
  dataLabels: { enabled: false },
  stroke: { curve: "smooth" },
});

onMounted(async () => {
  isComponentMounted = true;
  await nextTick(); // Ensure DOM is updated before initializing the chart
  fetchData();
});

onUnmounted(() => {
  isComponentMounted = false; // Prevent memory leaks by stopping updates
});

watch(
  () => [props.selectedItems, props.startDate, props.endDate, props.granularity],
  fetchData,
  { deep: true }
);

watch([
  () => props.selectedItems,
  () => props.startDate,
  () => props.endDate
], fetchData);
</script>

<style scoped>
.timeline-chart-container {
  width: 100%;
  height: 400px;
}
.no-data {
  text-align: center;
  margin-top: 165px;
}
.vue-apex-charts {
  margin: 10px;
}
</style>
