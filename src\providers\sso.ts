import { IOClient } from "@proptexx-com/io";

class SingleSignOnService
{
    private _provider: string;

    constructor(provider: string)
    {
        this._provider = provider;
    }
}

export function redirectSSO(client: IOClient)
{
    // Your client ID from Google Developer Console
    const clientId = '967598608391-dbnk0ttjvhg55pt9bj74glfmee26dfuc.apps.googleusercontent.com';

    // The redirect URI set in the Google Developer Console
    const redirectUri = encodeURIComponent('http://localhost:5501/sso');

    // Scopes you want to request (space-separated for multiple)
    const scope = encodeURIComponent(
        'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email'
    );

    // Use a random string for state to mitigate CSRF attacks, and store it for later validation
    const state = Math.random().toString(36).substring(2, 15);
    client.cache.set('sso-state', {
        state,
        initFromUrl: window.location.href
    });

    // Construct the OAuth 2.0 authorization URL
    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&state=${state}&access_type=offline&prompt=consent`;

    // Redirect the user to the Google OAuth page
    return authUrl;
}