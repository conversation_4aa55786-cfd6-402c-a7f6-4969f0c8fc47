<script setup lang="ts">
import { IQuery } from '@proptexx-com/io';
import { ref } from 'vue';
import DateTime from '@/components/datetime.vue';

const updates = ref(0);
const q: IQuery = {
    identifier: 'system.getTransactions',
    payload: {}
}

</script>

<template>

    <section>

        <div class="card">

            <div class="card-header">
                <h3>Transactions</h3>
                <div>
                    <button type="button" @click="updates++">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>

            <div class="card-body min-h-32">
                
                <n-query :config="q" :key="updates" #="result">

                    <div v-if="result && result.length > 0" class="overflow-x-auto">
                    
                        <table class="table is-striped">
    
                            <thead>
                                <tr>
                                    <th>Transaction</th>
                                    <th>Reference</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
    
                            <tbody>
                                <tr v-for="item of result" :key="item.id">
                                    <td>
                                        <strong>{{ item.id }}</strong><br>
                                        <small><DateTime :value="item.createdAt"></DateTime></small>
                                    </td>
                                    <td>
                                        <strong>{{ item.paymentChannel }}</strong><br>
                                        <small>{{ item.paymentRef }}</small>
                                    </td>
                                    <td>USD {{ item.amount }}</td>
                                </tr>
                            </tbody>
    
                        </table>
                    
                    </div>

                    <p v-else>No transactions</p>

                </n-query>

            </div>

        </div>

    </section>

</template>