<script setup lang="ts">
import { useIO } from '@proptexx-com/io';
const io = useIO();
</script>

<template>

    <router-view v-if="$route.name !== 'api'" />

    <div v-else class="card">
        
        <div class="card-header">
            <div>
                <h3>API</h3>
            </div>
        </div>

        <div class="card-body">

            <ol class="space-y-4">
                <li>
                    <router-link :to="{ name: 'api-get-started' }">
                        <h4>Get started</h4>
                    </router-link>
                    <p>This section provides a detailed introduction on how to integrate and utilize our API. It includes step-by-step instructions for installation and initial configuration, ensuring you can seamlessly incorporate our API into your applications and start benefiting from its features promptly.</p>
                </li>
                <li>
                    <router-link :to="{ name: 'access-keys' }">
                        <h4>Access Keys</h4>
                    </router-link>
                    <p>Generate new keys which are essential for authenticating each API request. This functionality allows you to maintain secure and controlled access to our API services, ensuring that only authorized users can connect and interact with the API.</p>
                </li>
                <li>
                    <router-link :to="{ name: 'requests' }">
                        <h4>Requests</h4>
                    </router-link>
                    <p>Monitor and analyze your API requests in this section. It offers comprehensive analytics on the images submitted, along with other pertinent details for each request, enabling you to gain valuable insights into usage patterns and operational metrics.</p>
                </li>
                <li>
                    <a href="https://docs.proptexx.com/api">
                        <h4>Documentation <i class="fa fa-external-link fa-xs ml-1"></i></h4>
                    </a>
                    <p>Comprehensive resource for detailed information about our APIs. This section includes the necessary technical documentation, user manuals, and FAQs to help you understand and troubleshoot any aspect of our APIs.</p>
                </li>
            </ol>

        </div>

    </div>
    
</template>