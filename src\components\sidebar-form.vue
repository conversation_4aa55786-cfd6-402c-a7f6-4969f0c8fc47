<script setup lang="ts">
import { ref } from 'vue';
import { useSidebar, useToast } from '@proptexx-com/vue';
import { FormEntry } from '@proptexx-com/forms';

export interface SidebarFormProps
{
    form: FormEntry;
    onSubmit: (form: FormEntry) => Promise<boolean>;
    labelSubmit?: string;
}

const { form, onSubmit, labelSubmit } = defineProps<SidebarFormProps>();
const sidebar = useSidebar();
const toast = useToast();
const updates = ref(0);

async function _onSubmit(e: SubmitEvent)
{
    updates.value++;
    await form.updateValidityAsync();

    if (form.invalid)
    {
        toast.error('Invalid form');
        return;
    }

    try {
        const isSuccess = await onSubmit(form);

        if (typeof isSuccess !== 'boolean' || isSuccess !== true)
        {
            throw new Error('Failed');
        }

        toast.success('Success');
        sidebar.close(true);
    } catch (error: any) {
        toast.error(error?.message || 'An error occured during persistance');
    }
}
</script>

<template>

    <n-form :form="form" @submit.prevent="_onSubmit">

        <input type="hidden" :key="updates" />
        <div class="space-x-2">
            <button type="submit" class="btn is-primary">{{ labelSubmit || 'Submit' }}</button>
            <button type="button" class="link" @click="() => sidebar.close()">Cancel</button>
        </div>

    </n-form>

</template>