<script setup lang="ts">
import { useIO } from "@proptexx-com/io";
import { useSidebar } from '@proptexx-com/vue';
import { update, updates } from '@/providers/workspace-updater';
import DateTime from '@/components/datetime.vue';

const io = useIO();
const sb = useSidebar();

const q = {
    identifier: 'workspace.GetAffiliates',
    payload: {}
};

function getStatusText(status: number)
{
    if (status === -2) return 'Error'
    if (status === -1) return 'Denied'
    if (status === 0) return 'Received'
    if (status === 1) return 'In Progress'
    if (status === 2) return 'Completed'
}

</script>

<template>

    <section class="card">

        <div class="card-header">
            <div>
                <h3>Affiliates</h3>
            </div>
            <div>
                <button type="button" @click="update()">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>

        <div class="card-body">

            <n-query :config="q" :key="updates" #="items">

                <div v-if="items.length > 0" class="overflow-x-auto">

                    <table class="table is-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th class="w-[175px]">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item of items" :key="item">
                                <td>{{ item.title }}</td>
                                <td><DateTime :value="item.createdAt"></DateTime></td>
                            </tr>
                        </tbody>
                    </table>

                </div>
    
                <div v-else>
                    <p>No affiliates have been registered</p>
                </div>
    
            </n-query>

        </div>

    </section>

</template>