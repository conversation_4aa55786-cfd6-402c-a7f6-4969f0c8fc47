<script setup>
import { ref, watch, defineProps, computed, onMounted, onBeforeUnmount } from "vue";
import { Chart as ChartJS, BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import ClientSelector from "@/components/dashboard/clientSelector.vue";
import Loader from "../loader.vue"; // Import Loader component

ChartJS.register(BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);

const io = useIO();

// Props from parent
const props = defineProps(["selectedItems", "startDate", "endDate"]);

const allData = ref([]); // Store all fetched data
const selectedEndpoints = ref([]); // Use an array instead of a single value
const isLoading = ref(false); // Add loading state

const chartData = ref({
  labels: [], // Endpoints will be used as the labels
  datasets: [] // Datasets for workspaces
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  indexAxis: 'y',
  plugins: {
    legend: { position: 'top' },
    title: { display: true, text: "API Requests by Endpoint" },
    tooltip: {
      callbacks: {
        label: function (tooltipItem) {
          return `${tooltipItem.dataset.label}: ${tooltipItem.raw}`;
        },
      },
    },
  },
  scales: {
    x: { stacked: true },
    y: { stacked: true }
  }
};

// Fetch data from API
const fetchData = async () => {
  isLoading.value = true; // Set loading to true
  try {
    const response = await io.queryAsync({
      identifier: "dashboard.getRequestCountPerEndpoint",
      payload: {
        ids: props.selectedItems,
        startDate: props.startDate,
        endDate: props.endDate,
      },
    }, true);

    allData.value = response;
    updateChartData();
  } catch (error) {
    console.error("Error fetching request data:", error);
  } finally {
    isLoading.value = false; // Set loading to false
  }
};

// Computed property to filter data by selected endpoints
const filteredData = computed(() => {
  return selectedEndpoints.value.length > 0
    ? allData.value.filter(entry => selectedEndpoints.value.includes(entry.endpoint))
    : allData.value;
});

// Dynamically calculate the height of the chart container
const chartHeight = computed(() => {
  const numItems = filteredData.value.length;
  const itemHeight = 20;
  const minHeight = 400;
  return Math.max(minHeight, numItems * itemHeight);
});

// Update chart based on filtered data
const updateChartData = () => {
  const endpoints = [...new Set(filteredData.value.map(entry => entry.endpoint))];
  const workspaces = [...new Set(filteredData.value.map(entry => entry.workspaceName))];

  const workspaceColors = {};
  const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40"];

  workspaces.forEach((workspace, index) => {
    workspaceColors[workspace] = colors[index % colors.length];
  });

  const datasets = workspaces.map(workspace => ({
    label: workspace, // Workspaces will be in the legend
    backgroundColor: workspaceColors[workspace],
    data: endpoints.map(endpoint => {
      const entry = filteredData.value.find(e => e.workspaceName === workspace && e.endpoint === endpoint);
      return entry ? entry.requestCount : 0;
    }),
  }));

  chartData.value = { labels: endpoints, datasets };
};

// Watch for changes
const stopWatchers = [
  watch(selectedEndpoints, updateChartData),
  watch([
    () => props.selectedItems,
    () => props.startDate,
    () => props.endDate
  ], fetchData, { deep: true })
];

// Clean up watchers to prevent memory leaks
onBeforeUnmount(() => {
  stopWatchers.forEach(unwatch => unwatch());
});

onMounted(fetchData);
</script>

<template>
  <div class="chart-container" :style="{ height: chartHeight + 'px' }">
    <!-- Multi-selection Endpoint Selector -->
    <div class="selector-container">
      <ClientSelector :clients="allData" v-model:selectedClients="selectedEndpoints" filterProperty="endpoint" />
    </div>

    <!-- Loading Indicator -->
    <div v-if="isLoading" class="loading-indicator">
      <Loader />
    </div>

    <!-- Chart -->

    <div v-else :style="{ height: chartHeight - 83 + 'px' }">
      <Bar v-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
      <p v-else class="no-data">No data available</p>
    </div>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.selector-container {
  margin-bottom: 20px;
}

.loading-indicator {
  text-align: center;
  font-size: 16px;
  color: #666;
}
</style>