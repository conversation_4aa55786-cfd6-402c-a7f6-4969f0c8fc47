import './index.pcss';
import '@fortawesome/fontawesome-free/css/all.css';
import { createIOVue } from '@proptexx-com/io-vue';
import { createVue } from '@proptexx-com/vue';
import router from './router';
import { FormDefaults } from '@proptexx-com/forms';
import { createVueForms } from '@proptexx-com/forms-vue';
import App from './components/app.vue';
import { checkAuth } from './providers/check-auth';
import { LocalStorageCache } from '@proptexx-com/io';

async function init()
{
    const { app, client } = createIOVue({
        rootComponent: App,
        ioOptions: {
            apiKey: import.meta.env.VITE_AUTH_KEY,
            baseUrl: import.meta.env.VITE_AUTH_URL,
            commandUrl: import.meta.env.VITE_COMMAND_URL,
            queryUrl: import.meta.env.VITE_QUERY_URL,
            clientVersion: 'portal-version',
            authenticatedResolver: (client) => {
                return client.hasClaim('$accountId') || client.hasClaim('accountId');
            },
            cache: {
                defaultEngine: 'local',
                engines: {
                    local: new LocalStorageCache('pt'),
                }
            }
        }
    });

    const isAuth = await checkAuth();
    if (!isAuth){
        location.href = `${import.meta.env.VITE_ACCOUNT_URL}?redirectUrl=${location.href}`;
        return;
    }

    const forms = createVueForms({ formDefaultsType: FormDefaults });

    app.use(createVue())
    app.use(forms);
    app.use(router);
    app.mount('#app');
}

init();
