/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'primary': '#BA4B30',
        'light': '#D26A52',
        'dark': '#8F3623'
      },
      keyframes: {
        slide: {
          '0%': { left: '-100%', width: '0%' },
          '50%': { left: '0%', width: '100%' },
          '100%': { left: '100%', width: '0%' },
        },
      },
      animation: {
        'slide-infinite': 'slide 2s linear infinite',
      },
    },
  },
  plugins: []
}
