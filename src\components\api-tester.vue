<script setup lang="ts">
import { Form, Val } from '@proptexx-com/forms';
import { useIO } from '@proptexx-com/io';
import { ref, onUnmounted } from 'vue';
import Loader from '@/components/loader.vue';

const { url } = defineProps<{
    url: string
}>();

const io = useIO();
const result = ref<any>();
const notification = ref<{class: string, message: string}>();
const isLoading = ref(false);
const form = Form.object({
    url: Form.string({
        value: url,
        readonly: true,
        validators: [Val.isRequired]
    }),
    inputImageUrl: Form.string({
        placeholder: 'https://',
        validators: [Val.isRequired, Val.pattern(/^(http|https)\:\/\/.+/, 'i')]
    }),
    apiKey: Form.string({
        value: '64ddc73ce7f4771179c3d67f',
        description: 'Use the legacy Api Key for now',
        validators: [Val.isRequired]
    })
});

let isComponentActive = true; // To prevent memory leaks during async operations

async function submit() {
    try {
        notification.value = undefined;
        result.value = undefined;
        isLoading.value = true;

        if (form.invalid) return;

        const response = await io.executeAsync({
            identifier: 'service.testRequest',
            payload: form.value
        });

        if (!isComponentActive) return; // Prevent updating state if component is unmounted

        if (!response?.isSuccess) {
            throw Error(response?.errorMessage || 'Unable to execute API call');
        }

        const json = response.result.output as string;
        if (json.trim().startsWith('{')) {
            result.value = JSON.parse(response.result.output);
        } else {
            throw new Error(response.result.output);
        }
    } catch (error: any) {
        if (isComponentActive) {
            notification.value = { message: error?.message || error, class: 'is-error' };
        }
    } finally {
        if (isComponentActive) {
            isLoading.value = false;
        }
    }
}

// Cleanup to prevent memory leaks
onUnmounted(() => {
    isComponentActive = false;
});
</script>

<template>
    <div class="card">
        <div class="card-header">
            <h3>Test the API Service</h3>
        </div>

        <div class="card-body">
            <n-form :form="form" @submit.prevent="submit">
                <div v-if="notification?.message" class="notification" :class="notification.class">
                    {{ notification.message }}
                </div>

                <div class="space-y-4">
                    <button type="submit" class="btn is-primary" :disabled="isLoading">
                        Run
                    </button>
                </div>
            </n-form>
        </div>

        <div v-if="isLoading" class="card-body">
            <Loader />
        </div>

        <div v-if="result" class="card-body">
            <pre>{{ result }}</pre>
        </div>
    </div>
</template>