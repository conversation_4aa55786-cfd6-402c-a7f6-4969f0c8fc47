<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  computed,
  onMounted,
  onUnmounted,
} from "vue";
import {
  Chart as ChartJS,
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "vue-chartjs";
import { useIO } from "@proptexx-com/io";
import Loader from "../loader.vue";

// Register chart components
ChartJS.register(
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend
);

// Setup
const io = useIO();
const emit = defineEmits(["update:selectedItems"]);
const props = defineProps(["selectedItems", "startDate", "endDate"]);

// Local state
const allData = ref([]);
const selectedRenderUrls = ref([]);
const isLoading = ref(false); // Loader state

// Sync local state with prop
watch(
  () => props.selectedItems,
  (newVal) => {
    selectedRenderUrls.value = [...newVal];
  }
);

// Emit updates to parent
watch(selectedRenderUrls, () => {
  emit("update:selectedItems", selectedRenderUrls.value);
});

// Computed filter
const filteredData = computed(() => {
  return selectedRenderUrls.value.length > 0
    ? allData.value.filter((entry) =>
      selectedRenderUrls.value.includes(entry.renderUrl)
    )
    : allData.value;
});

// Chart state
const chartData = ref({
  labels: [],
  datasets: [],
});
const chartOptions = computed(() => {
  const max = Math.max(...chartData.value.datasets[0]?.data || [0]);

  return {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: "y",
    plugins: {
      legend: { display: false },
      title: { display: false },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            return `${tooltipItem.dataset.label}: ${tooltipItem.raw}`;
          },
        },
      },
    },
    scales: {
      x: {
        stacked: true,
        beginAtZero: true,
        max: Math.max(max + 5, 10),
        ticks: {
          precision: 0,
          stepSize: Math.max(Math.ceil(max / 5), 1),
        },
      },
      y: {
        stacked: true,
      },
    },
  };
});

// Fetch backend data
const fetchData = async () => {
  isLoading.value = true; // Start loader
  try {
    const response = await io.queryAsync(
      {
        identifier: "dashboard.getTopRenderedProperties",
        payload: {
          ids: props.selectedItems,
          startDate: props.startDate,
          endDate: props.endDate,
        },
      },
      true
    );

    allData.value = response;
    updateChartData();
  } catch (error) {
    console.error("Error fetching request data:", error);
  } finally {
    isLoading.value = false; // Stop loader
  }
};

// Update chart
const updateChartData = () => {
  const renderUrls = filteredData.value.map((entry) => entry.renderUrl);
  const requestCounts = filteredData.value.map((entry) => entry.renderCount);

  const backgroundColors = [
    "#FF6384",
    "#36A2EB",
    "#FFCE56",
    "#4BC0C0",
    "#9966FF",
  ];

  chartData.value = {
    labels: renderUrls,
    datasets: [
      {
        label: "Requests",
        backgroundColor: backgroundColors,
        data: requestCounts,
      },
    ],
  };
};

// Watch changes
const stopWatchers = [
  watch(
    [() => props.selectedItems, () => props.startDate, () => props.endDate],
    fetchData,
    { deep: true }
  ),
  watch(selectedRenderUrls, updateChartData),
  watch([
    () => props.selectedItems,
    () => props.startDate,
    () => props.endDate
  ], fetchData),
];

onMounted(fetchData);

onUnmounted(() => {
  // Clean up watchers to prevent memory leaks
  stopWatchers.forEach((stop) => stop());
});
</script>

<template>
  <div class="chart-wrapper" :style="{ height: `${Math.max(chartData.labels.length * 40, 300)}px` }">
    <div class="loader-container" v-if="isLoading">
      <Loader />
    </div>
    <Bar v-else-if="chartData.labels.length > 0" :data="chartData" :options="chartOptions" />
    <p v-else class="no-data">No data available</p>
  </div>
</template>

<style scoped>
.chart-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>